<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Chart of Accounts Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the standard chart of accounts for the
    | loan management system. Each account has a code, name, type, and
    | normal balance (debit or credit).
    |
    */

    'account_types' => [
        'asset' => 'Asset',
        'liability' => 'Liability', 
        'equity' => 'Equity',
        'income' => 'Income',
        'expense' => 'Expense',
    ],

    'normal_balances' => [
        'asset' => 'debit',
        'liability' => 'credit',
        'equity' => 'credit', 
        'income' => 'credit',
        'expense' => 'debit',
    ],

    'default_accounts' => [
        // ASSETS (1000-1999)
        [
            'code' => '1000',
            'name' => 'Cash in Hand',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Physical cash on hand'
        ],
        [
            'code' => '1010',
            'name' => 'Cash in Bank - Main Account',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Primary bank account'
        ],
        [
            'code' => '1020',
            'name' => 'Cash in Bank - Savings Account',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Savings bank account'
        ],
        [
            'code' => '1100',
            'name' => 'Loans Receivable',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Outstanding loans to borrowers'
        ],
        [
            'code' => '1110',
            'name' => 'Interest Receivable',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Accrued interest on loans'
        ],
        [
            'code' => '1120',
            'name' => 'Allowance for Doubtful Loans',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Provision for potential loan losses'
        ],
        [
            'code' => '1200',
            'name' => 'Office Equipment',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Office furniture and equipment'
        ],
        [
            'code' => '1210',
            'name' => 'Computer Equipment',
            'type' => 'asset',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Computers and IT equipment'
        ],

        // LIABILITIES (2000-2999)
        [
            'code' => '2000',
            'name' => 'Accounts Payable',
            'type' => 'liability',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Money owed to suppliers'
        ],
        [
            'code' => '2100',
            'name' => 'Loan Payable',
            'type' => 'liability',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Loans from banks or investors'
        ],
        [
            'code' => '2200',
            'name' => 'Interest Payable',
            'type' => 'liability',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Accrued interest on borrowed funds'
        ],
        [
            'code' => '2300',
            'name' => 'Customer Deposits',
            'type' => 'liability',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Deposits received from customers'
        ],

        // EQUITY (3000-3999)
        [
            'code' => '3000',
            'name' => 'Owner\'s Capital',
            'type' => 'equity',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Initial capital investment'
        ],
        [
            'code' => '3100',
            'name' => 'Retained Earnings',
            'type' => 'equity',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Accumulated profits'
        ],

        // INCOME (4000-4999)
        [
            'code' => '4000',
            'name' => 'Interest Income',
            'type' => 'income',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Interest earned on loans'
        ],
        [
            'code' => '4100',
            'name' => 'Processing Fees',
            'type' => 'income',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Loan processing and application fees'
        ],
        [
            'code' => '4200',
            'name' => 'Late Payment Fees',
            'type' => 'income',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Penalties for late payments'
        ],
        [
            'code' => '4300',
            'name' => 'Other Income',
            'type' => 'income',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Miscellaneous income'
        ],

        // EXPENSES (5000-5999)
        [
            'code' => '5000',
            'name' => 'Office Rent',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Monthly office rent'
        ],
        [
            'code' => '5100',
            'name' => 'Salaries and Wages',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Employee compensation'
        ],
        [
            'code' => '5200',
            'name' => 'Utilities',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Electricity, water, internet'
        ],
        [
            'code' => '5300',
            'name' => 'Marketing and Advertising',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Promotional expenses'
        ],
        [
            'code' => '5400',
            'name' => 'Bad Debt Expense',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Loans written off as uncollectible'
        ],
        [
            'code' => '5500',
            'name' => 'Office Supplies',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Stationery and office materials'
        ],
        [
            'code' => '5600',
            'name' => 'Professional Fees',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Legal, accounting, and consulting fees'
        ],
        [
            'code' => '5700',
            'name' => 'Bank Charges',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Banking fees and charges'
        ],
        [
            'code' => '5800',
            'name' => 'Depreciation Expense',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Depreciation of fixed assets'
        ],
        [
            'code' => '5900',
            'name' => 'Miscellaneous Expenses',
            'type' => 'expense',
            'parent_code' => null,
            'is_active' => true,
            'description' => 'Other operating expenses'
        ],
    ],
];
