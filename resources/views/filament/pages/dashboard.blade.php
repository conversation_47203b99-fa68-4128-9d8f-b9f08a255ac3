<x-filament-panels::page>
    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
    @endpush

    {{-- Welcome Header --}}
    <div class="mb-6">
        <div class="dashboard-header bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold mb-2">
                        Welcome back, {{ auth()->user()->name }}! 👋
                    </h1>
                    <p class="text-blue-100">
                        Here's what's happening with your loan portfolio today.
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="text-right">
                        <div class="text-blue-100 text-sm">{{ now()->format('l') }}</div>
                        <div class="text-white font-semibold">{{ now()->format('F j, Y') }}</div>
                        <div class="text-blue-100 text-sm">{{ now()->format('g:i A') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="mb-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{{ route('filament.admin.resources.loans.create') }}"
               class="quick-action-card bg-white rounded-lg p-4 shadow hover:shadow-md transition-shadow border border-gray-200 hover:border-blue-300">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-lg mr-3">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900">New Loan</div>
                        <div class="text-xs text-gray-500">Create loan</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.borrowers.create') }}"
               class="quick-action-card bg-white rounded-lg p-4 shadow hover:shadow-md transition-shadow border border-gray-200 hover:border-green-300">
                <div class="flex items-center">
                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900">New Borrower</div>
                        <div class="text-xs text-gray-500">Add customer</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.repayments.create') }}"
               class="quick-action-card bg-white rounded-lg p-4 shadow hover:shadow-md transition-shadow border border-gray-200 hover:border-yellow-300">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900">Record Payment</div>
                        <div class="text-xs text-gray-500">Add repayment</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.loan-notifications.index') }}"
               class="quick-action-card bg-white rounded-lg p-4 shadow hover:shadow-md transition-shadow border border-gray-200 hover:border-red-300">
                <div class="flex items-center">
                    <div class="bg-red-100 p-2 rounded-lg mr-3">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707A5 5 0 015.5 3H9a1 1 0 011 1v5.586l4.707-4.707A1 1 0 0116 4h3a1 1 0 011 1v3a1 1 0 01-.293.707L15 13.414V9a1 1 0 00-1-1H9.414l-4.586 4.586z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900">Notifications</div>
                        <div class="text-xs text-gray-500">View alerts</div>
                    </div>
                </div>
            </a>
        </div>
    </div>

    {{-- Dashboard Widgets --}}
    <div class="space-y-6">
        {{-- Essential Business Metrics --}}
        @livewire(\Livewire\Livewire::getAlias(\App\Filament\Widgets\BusinessOverviewStats::class))

        {{-- Performance Chart --}}
        <div class="w-full">
            @livewire(\Livewire\Livewire::getAlias(\App\Filament\Widgets\LoanPerformanceChart::class))
        </div>

        {{-- Quick Links to Detailed Pages --}}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
            <a href="{{ route('filament.admin.resources.loans.index') }}"
               class="quick-action-card bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 shadow hover:shadow-lg transition-all border border-blue-200 hover:border-blue-300">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-blue-900">Loan Management</h3>
                        <p class="text-blue-700 text-sm mt-1">View detailed loan statistics and status breakdown</p>
                    </div>
                    <div class="bg-blue-200 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.borrowers.index') }}"
               class="quick-action-card bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 shadow hover:shadow-lg transition-all border border-green-200 hover:border-green-300">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-green-900">Customer Management</h3>
                        <p class="text-green-700 text-sm mt-1">View borrower statistics and customer insights</p>
                    </div>
                    <div class="bg-green-200 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.loan-notifications.index') }}"
               class="quick-action-card bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-6 shadow hover:shadow-lg transition-all border border-red-200 hover:border-red-300">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-red-900">Alert Center</h3>
                        <p class="text-red-700 text-sm mt-1">Monitor critical alerts and notification statistics</p>
                    </div>
                    <div class="bg-red-200 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707A5 5 0 015.5 3H9a1 1 0 011 1v5.586l4.707-4.707A1 1 0 0116 4h3a1 1 0 011 1v3a1 1 0 01-.293.707L15 13.414V9a1 1 0 00-1-1H9.414l-4.586 4.586z"></path>
                        </svg>
                    </div>
                </div>
            </a>
        </div>
    </div>

    {{-- Footer --}}
    <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="text-center text-sm text-gray-500">
            <p>{{ config('app.name') }} - Loan Management System</p>
            <p class="mt-1">Last updated: {{ now()->format('M j, Y \a\t g:i A') }}</p>
        </div>
    </div>
</x-filament-panels::page>
