<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Date Filter Form -->
        <div class="bg-white rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Balance Sheet Report -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    Balance Sheet as of {{ \Carbon\Carbon::parse($data['as_of_date'] ?? now())->format('F j, Y') }}
                </h3>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
                <!-- Assets -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">ASSETS</h4>
                    <div class="space-y-2">
                        @forelse($balanceSheetData['assets'] ?? [] as $assetData)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-700">{{ $assetData['account']->name }}</span>
                                <span class="text-sm font-medium">${{ number_format($assetData['balance'], 2) }}</span>
                            </div>
                        @empty
                            <div class="text-sm text-gray-500">No assets found</div>
                        @endforelse
                        <div class="border-t pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Total Assets</span>
                                <span>${{ number_format($balanceSheetData['total_assets'] ?? 0, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liabilities & Equity -->
                <div>
                    <!-- Liabilities -->
                    <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">LIABILITIES</h4>
                    <div class="space-y-2 mb-6">
                        @forelse($balanceSheetData['liabilities'] ?? [] as $liabilityData)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-700">{{ $liabilityData['account']->name }}</span>
                                <span class="text-sm font-medium">${{ number_format($liabilityData['balance'], 2) }}</span>
                            </div>
                        @empty
                            <div class="text-sm text-gray-500">No liabilities found</div>
                        @endforelse
                        <div class="border-t pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Total Liabilities</span>
                                <span>${{ number_format($balanceSheetData['total_liabilities'] ?? 0, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Equity -->
                    <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">EQUITY</h4>
                    <div class="space-y-2">
                        @forelse($balanceSheetData['equity'] ?? [] as $equityData)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-700">{{ $equityData['account']->name }}</span>
                                <span class="text-sm font-medium">${{ number_format($equityData['balance'], 2) }}</span>
                            </div>
                        @empty
                            <div class="text-sm text-gray-500">No equity accounts found</div>
                        @endforelse
                        <div class="border-t pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Total Equity</span>
                                <span>${{ number_format($balanceSheetData['total_equity'] ?? 0, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Total Liabilities & Equity -->
                    <div class="border-t-2 border-gray-300 pt-4 mt-6">
                        <div class="flex justify-between font-bold text-lg">
                            <span>Total Liabilities & Equity</span>
                            <span>${{ number_format($balanceSheetData['total_liabilities_equity'] ?? 0, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Balance Status -->
            <div class="px-6 py-4 border-t border-gray-200">
                @if(($balanceSheetData['is_balanced'] ?? false))
                    <div class="flex items-center text-green-600">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Balance Sheet is balanced (Assets = Liabilities + Equity)</span>
                    </div>
                @else
                    <div class="flex items-center text-red-600">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Balance Sheet is NOT balanced</span>
                        <span class="ml-2 text-sm">
                            (Difference: ${{ number_format(abs(($balanceSheetData['total_assets'] ?? 0) - ($balanceSheetData['total_liabilities_equity'] ?? 0)), 2) }})
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-filament-panels::page>
