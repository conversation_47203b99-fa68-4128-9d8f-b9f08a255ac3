<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Date Filter Form -->
        <div class="bg-white rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Income Statement Report -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    Income Statement for {{ \Carbon\Carbon::parse($data['start_date'] ?? now()->startOfMonth())->format('M j, Y') }}
                    to {{ \Carbon\Carbon::parse($data['end_date'] ?? now()->endOfMonth())->format('M j, Y') }}
                </h3>
            </div>

            <div class="p-6">
                <!-- Income Section -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">INCOME</h4>
                    <div class="space-y-2">
                        @forelse($incomeStatementData['income'] ?? [] as $incomeData)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-700 pl-4">{{ $incomeData['account']->name }}</span>
                                <span class="text-sm font-medium">${{ number_format($incomeData['balance'], 2) }}</span>
                            </div>
                        @empty
                            <div class="text-sm text-gray-500 pl-4">No income recorded for this period</div>
                        @endforelse
                        <div class="border-t pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Total Income</span>
                                <span>${{ number_format($incomeStatementData['total_income'] ?? 0, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expenses Section -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">EXPENSES</h4>
                    <div class="space-y-2">
                        @forelse($incomeStatementData['expenses'] ?? [] as $expenseData)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-700 pl-4">{{ $expenseData['account']->name }}</span>
                                <span class="text-sm font-medium">${{ number_format($expenseData['balance'], 2) }}</span>
                            </div>
                        @empty
                            <div class="text-sm text-gray-500 pl-4">No expenses recorded for this period</div>
                        @endforelse
                        <div class="border-t pt-2 mt-4">
                            <div class="flex justify-between font-semibold">
                                <span>Total Expenses</span>
                                <span>${{ number_format($incomeStatementData['total_expenses'] ?? 0, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Net Income -->
                <div class="border-t-2 border-gray-300 pt-4">
                    <div class="flex justify-between font-bold text-lg">
                        <span>Net Income</span>
                        <span class="{{ ($incomeStatementData['net_income'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            ${{ number_format($incomeStatementData['net_income'] ?? 0, 2) }}
                        </span>
                    </div>
                    @if(($incomeStatementData['net_income'] ?? 0) < 0)
                        <p class="text-sm text-red-600 mt-1">Net Loss</p>
                    @endif
                </div>

                <!-- Summary Stats -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-green-800">Total Income</div>
                        <div class="text-lg font-bold text-green-900">${{ number_format($incomeStatementData['total_income'] ?? 0, 2) }}</div>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-red-800">Total Expenses</div>
                        <div class="text-lg font-bold text-red-900">${{ number_format($incomeStatementData['total_expenses'] ?? 0, 2) }}</div>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm font-medium text-blue-800">Net Income</div>
                        <div class="text-lg font-bold {{ ($incomeStatementData['net_income'] ?? 0) >= 0 ? 'text-blue-900' : 'text-red-900' }}">
                            ${{ number_format($incomeStatementData['net_income'] ?? 0, 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
