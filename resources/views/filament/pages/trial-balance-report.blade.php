<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Date Filter Form -->
        <div class="bg-white rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Trial Balance Report -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    Trial Balance as of {{ \Carbon\Carbon::parse($data['as_of_date'] ?? now())->format('F j, Y') }}
                </h3>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Account Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Account Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Debit Balance
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Credit Balance
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($trialBalanceData['accounts'] ?? [] as $accountData)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $accountData['account']->code }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $accountData['account']->name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        @switch($accountData['account']->type)
                                            @case('asset')
                                                bg-green-100 text-green-800
                                                @break
                                            @case('liability')
                                                bg-yellow-100 text-yellow-800
                                                @break
                                            @case('equity')
                                                bg-blue-100 text-blue-800
                                                @break
                                            @case('income')
                                                bg-purple-100 text-purple-800
                                                @break
                                            @case('expense')
                                                bg-red-100 text-red-800
                                                @break
                                        @endswitch
                                    ">
                                        {{ ucfirst($accountData['account']->type) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    @if($accountData['debit_balance'] > 0)
                                        ${{ number_format($accountData['debit_balance'], 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                    @if($accountData['credit_balance'] > 0)
                                        ${{ number_format($accountData['credit_balance'], 2) }}
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No accounts with balances found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                    <tfoot class="bg-gray-50 font-semibold">
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-sm text-gray-900">
                                <strong>TOTALS</strong>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 text-right">
                                <strong>${{ number_format($trialBalanceData['total_debits'] ?? 0, 2) }}</strong>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 text-right">
                                <strong>${{ number_format($trialBalanceData['total_credits'] ?? 0, 2) }}</strong>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Balance Status -->
            <div class="px-6 py-4 border-t border-gray-200">
                @if(($trialBalanceData['is_balanced'] ?? false))
                    <div class="flex items-center text-green-600">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Trial Balance is balanced</span>
                    </div>
                @else
                    <div class="flex items-center text-red-600">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Trial Balance is NOT balanced</span>
                        <span class="ml-2 text-sm">
                            (Difference: ${{ number_format(abs(($trialBalanceData['total_debits'] ?? 0) - ($trialBalanceData['total_credits'] ?? 0)), 2) }})
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-filament-panels::page>
