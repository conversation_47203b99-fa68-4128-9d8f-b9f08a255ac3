<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $notification->title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loan-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .loan-details h3 {
            margin-top: 0;
            color: #495057;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
        }
        .detail-label {
            font-weight: bold;
            color: #6c757d;
        }
        .detail-value {
            color: #495057;
        }
        .amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #dc3545;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
        }
        .contact-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ config('app.name') }}</h1>
        <p>Loan Management System</p>
    </div>

    <div class="content">
        <h2>Dear {{ $borrower->first_name }} {{ $borrower->last_name }},</h2>

        @if($notification->type === 'payment_reminder')
            <div class="alert alert-warning">
                <strong>Payment Reminder</strong><br>
                This is a friendly reminder about your upcoming loan payment.
            </div>
        @elseif($notification->type === 'overdue')
            <div class="alert alert-danger">
                <strong>Overdue Payment Notice</strong><br>
                Your loan payment is now overdue. Immediate action is required.
            </div>
        @elseif($notification->type === 'bad_debt')
            <div class="alert alert-danger">
                <strong>Important: Account Review Required</strong><br>
                Your account requires immediate attention to avoid further action.
            </div>
        @endif

        <p>{{ $notification->message }}</p>

        <div class="loan-details">
            <h3>Loan Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Loan Number:</span>
                <span class="detail-value">{{ $loan->loan_number }}</span>
            </div>

            @if($notification->amount)
            <div class="detail-row">
                <span class="detail-label">Amount Due:</span>
                <span class="detail-value amount">${{ number_format($notification->amount, 2) }}</span>
            </div>
            @endif

            @if($notification->due_date)
            <div class="detail-row">
                <span class="detail-label">Due Date:</span>
                <span class="detail-value">{{ $notification->due_date->format('F j, Y') }}</span>
            </div>
            @endif

            @if($notification->days_overdue && $notification->days_overdue > 0)
            <div class="detail-row">
                <span class="detail-label">Days Overdue:</span>
                <span class="detail-value" style="color: #dc3545; font-weight: bold;">{{ $notification->days_overdue }} days</span>
            </div>
            @endif

            <div class="detail-row">
                <span class="detail-label">Current Balance:</span>
                <span class="detail-value">${{ number_format($loan->balance, 2) }}</span>
            </div>

            <div class="detail-row">
                <span class="detail-label">Original Amount:</span>
                <span class="detail-value">${{ number_format($loan->principal_amount, 2) }}</span>
            </div>
        </div>

        @if($notification->type === 'payment_reminder')
            <p><strong>Action Required:</strong> Please ensure your payment is made by the due date to avoid late fees and maintain your good standing.</p>
        @elseif($notification->type === 'overdue')
            <p><strong>Immediate Action Required:</strong> Your payment is now overdue. Please contact us immediately to arrange payment and avoid additional penalties or collection actions.</p>
        @elseif($notification->type === 'bad_debt')
            <p><strong>Urgent Action Required:</strong> Your account has been significantly overdue. Please contact us immediately to discuss payment arrangements and avoid further collection actions.</p>
        @endif

        <div class="contact-info">
            <h4>Need Help or Have Questions?</h4>
            <p>If you have any questions about your loan or need to discuss payment arrangements, please contact us immediately:</p>
            <p>
                <strong>Phone:</strong> {{ config('company.phone', '(*************') }}<br>
                <strong>Email:</strong> {{ config('company.email', 'support@' . config('app.domain', 'example.com')) }}<br>
                <strong>Office Hours:</strong> Monday - Friday, 9:00 AM - 5:00 PM
            </p>
        </div>
    </div>

    <div class="footer">
        <p>This is an automated message from {{ config('app.name') }}.</p>
        <p>Please do not reply directly to this email. For assistance, use the contact information provided above.</p>
        <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
    </div>
</body>
</html>
