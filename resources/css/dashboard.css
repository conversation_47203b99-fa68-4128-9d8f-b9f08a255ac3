/* Custom Dashboard Styles */

/* Smooth transitions for all interactive elements */
.fi-stats-card,
.fi-ta-table,
.fi-wi-chart {
    transition: all 0.2s ease-in-out;
}

/* Enhanced card hover effects */
.fi-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Professional gradient backgrounds for stats */
.fi-stats-card .fi-stats-card-value {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced table styling */
.fi-ta-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Chart container styling */
.fi-wi-chart {
    border-radius: 12px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 1rem;
}

/* Professional color scheme for badges */
.fi-badge {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Enhanced notification styling */
.fi-notification {
    border-left: 4px solid;
    border-radius: 8px;
}

.fi-notification.fi-notification-danger {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.fi-notification.fi-notification-warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.fi-notification.fi-notification-success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

/* Professional button styling */
.fi-btn {
    transition: all 0.2s ease-in-out;
    font-weight: 600;
}

.fi-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dashboard header styling */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

/* Quick action cards */
.quick-action-card {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
}

/* Professional spacing and typography */
.dashboard-section {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Responsive grid improvements */
@media (min-width: 768px) {
    .dashboard-grid {
        display: grid;
        gap: 1.5rem;
    }
    
    .dashboard-grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Loading states */
.fi-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Professional scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1, #94a3b8);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8, #64748b);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dashboard-header {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }
    
    .fi-wi-chart {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }
}
