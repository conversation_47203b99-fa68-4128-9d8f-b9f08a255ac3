<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Services\DoubleEntryService;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\User;

class DoubleEntryServiceTest extends TestCase
{
    use RefreshDatabase;

    protected DoubleEntryService $doubleEntryService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->doubleEntryService = app(DoubleEntryService::class);

        // Create test accounts
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        Account::create([
            'code' => '1000',
            'name' => 'Cash in Hand',
            'type' => 'asset',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '1010',
            'name' => 'Cash in Bank',
            'type' => 'asset',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '1100',
            'name' => 'Loans Receivable',
            'type' => 'asset',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '4000',
            'name' => 'Interest Income',
            'type' => 'income',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '5000',
            'name' => 'Office Rent',
            'type' => 'expense',
            'is_active' => true,
        ]);
    }

    public function test_can_create_simple_journal_entry(): void
    {
        $journalEntry = $this->doubleEntryService->createSimpleEntry(
            debitAccountCode: '1100',
            creditAccountCode: '1010',
            amount: 1000.00,
            description: 'Test loan disbursement'
        );

        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertEquals('Test loan disbursement', $journalEntry->description);
        $this->assertEquals(1000.00, $journalEntry->total_amount);
        $this->assertCount(2, $journalEntry->lines);

        // Check that debits equal credits
        $this->assertTrue($journalEntry->isBalanced());
    }

    public function test_can_record_loan_disbursement(): void
    {
        $journalEntry = $this->doubleEntryService->recordLoanDisbursement(
            amount: 5000.00,
            options: ['description' => 'Loan disbursement to John Doe']
        );

        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertEquals('Loan disbursement to John Doe', $journalEntry->description);
        $this->assertEquals(5000.00, $journalEntry->total_amount);

        // Check account balances after posting
        $this->doubleEntryService->postJournalEntry($journalEntry);

        $loansReceivable = Account::where('code', '1100')->first();
        $cashInBank = Account::where('code', '1010')->first();

        $this->assertEquals(5000.00, $loansReceivable->calculateBalance());
        $this->assertEquals(-5000.00, $cashInBank->calculateBalance());
    }

    public function test_can_record_loan_repayment(): void
    {
        // First create a loan disbursement
        $disbursement = $this->doubleEntryService->recordLoanDisbursement(5000.00);
        $this->doubleEntryService->postJournalEntry($disbursement);

        // Then record a repayment
        $repayment = $this->doubleEntryService->recordLoanRepayment(
            principalAmount: 2000.00,
            interestAmount: 100.00
        );

        $this->assertInstanceOf(JournalEntry::class, $repayment);
        $this->assertEquals(2100.00, $repayment->total_amount);

        // Post the repayment
        $this->doubleEntryService->postJournalEntry($repayment);

        // Check balances
        $loansReceivable = Account::where('code', '1100')->first();
        $cashInBank = Account::where('code', '1010')->first();
        $interestIncome = Account::where('code', '4000')->first();

        $this->assertEquals(3000.00, $loansReceivable->calculateBalance()); // 5000 - 2000
        $this->assertEquals(-2900.00, $cashInBank->calculateBalance()); // -5000 + 2100
        $this->assertEquals(100.00, $interestIncome->calculateBalance());
    }

    public function test_trial_balance_is_balanced(): void
    {
        // Create some transactions
        $entry1 = $this->doubleEntryService->recordLoanDisbursement(1000.00);
        $this->doubleEntryService->postJournalEntry($entry1);

        $entry2 = $this->doubleEntryService->recordExpense('5000', 500.00, 'Office rent');
        $this->doubleEntryService->postJournalEntry($entry2);

        // Get trial balance
        $trialBalance = $this->doubleEntryService->getTrialBalance();

        $this->assertTrue($trialBalance['is_balanced']);
        $this->assertEquals($trialBalance['total_debits'], $trialBalance['total_credits']);
    }

    public function test_cannot_create_unbalanced_journal_entry(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Journal entry is not balanced');

        $this->doubleEntryService->createJournalEntry([
            'description' => 'Unbalanced entry',
            'lines' => [
                [
                    'account_id' => Account::where('code', '1000')->first()->id,
                    'type' => 'debit',
                    'amount' => 1000.00,
                ],
                [
                    'account_id' => Account::where('code', '1010')->first()->id,
                    'type' => 'credit',
                    'amount' => 500.00, // This makes it unbalanced
                ],
            ],
        ]);
    }
}
