<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Loan;
use App\Models\Borrower;
use App\Models\LoanType;
use App\Models\Repayments;
use App\Models\Account;
use App\Models\JournalEntry;
use App\Services\LoanAccountingService;

class LoanAccountingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected LoanAccountingService $loanAccountingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->loanAccountingService = app(LoanAccountingService::class);

        // Create test accounts
        $this->createTestAccounts();
    }

    protected function createTestAccounts(): void
    {
        Account::create([
            'code' => '1010',
            'name' => 'Cash in Bank - Main Account',
            'type' => 'asset',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '1100',
            'name' => 'Loans Receivable',
            'type' => 'asset',
            'is_active' => true,
        ]);

        Account::create([
            'code' => '4000',
            'name' => 'Interest Income',
            'type' => 'income',
            'is_active' => true,
        ]);
    }

    public function test_loan_disbursement_creates_journal_entry(): void
    {
        // Create test data
        $borrower = Borrower::factory()->create();
        $loanType = LoanType::factory()->create();

        $loan = Loan::factory()->create([
            'borrower_id' => $borrower->id,
            'loan_type_id' => $loanType->id,
            'loan_status' => 'approved',
            'principal_amount' => 5000.00,
            'loan_release_date' => now()->toDateString(),
        ]);

        // Record the disbursement
        $journalEntry = $this->loanAccountingService->recordLoanDisbursement($loan);

        // Assertions
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertTrue($journalEntry->is_posted);
        $this->assertEquals(5000.00, $journalEntry->total_amount);
        $this->assertCount(2, $journalEntry->lines);

        // Check account balances
        $loansReceivable = Account::where('code', '1100')->first();
        $cashInBank = Account::where('code', '1010')->first();

        $this->assertEquals(5000.00, $loansReceivable->calculateBalance());
        $this->assertEquals(-5000.00, $cashInBank->calculateBalance());
    }

    public function test_loan_repayment_creates_journal_entry(): void
    {
        // Create test data
        $borrower = Borrower::factory()->create();
        $loanType = LoanType::factory()->create();

        $loan = Loan::factory()->create([
            'borrower_id' => $borrower->id,
            'loan_type_id' => $loanType->id,
            'loan_status' => 'approved',
            'principal_amount' => 5000.00,
            'balance' => 5000.00,
        ]);

        // First record disbursement
        $this->loanAccountingService->recordLoanDisbursement($loan);

        // Create a repayment
        $repayment = Repayments::create([
            'loan_id' => $loan->id,
            'payments' => 1000.00,
            'balance' => 4000.00,
            'payments_method' => 'cash',
            'principal' => 5000.00,
            'loan_number' => $loan,
        ]);

        // Record the repayment
        $journalEntry = $this->loanAccountingService->recordLoanRepayment($repayment);

        // Assertions
        $this->assertInstanceOf(JournalEntry::class, $journalEntry);
        $this->assertTrue($journalEntry->is_posted);
        $this->assertEquals(1000.00, $journalEntry->total_amount);

        // Check account balances after repayment
        $loansReceivable = Account::where('code', '1100')->first();
        $cashInBank = Account::where('code', '1010')->first();

        $this->assertEquals(4000.00, $loansReceivable->calculateBalance()); // 5000 - 1000
        $this->assertEquals(-4000.00, $cashInBank->calculateBalance()); // -5000 + 1000
    }
}
