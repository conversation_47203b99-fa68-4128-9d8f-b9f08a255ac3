<?php

namespace Database\Seeders;

use App\Models\NotificationSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NotificationSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Payment Reminder Settings
            [
                'key' => 'payment_reminder_days',
                'name' => 'Payment Reminder Days',
                'description' => 'Number of days before due date to send payment reminders',
                'value' => '7',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:30'],
                'category' => 'payment_reminders',
                'is_active' => true,
            ],
            [
                'key' => 'payment_reminder_enabled',
                'name' => 'Enable Payment Reminders',
                'description' => 'Enable automatic payment reminder notifications',
                'value' => '1',
                'type' => 'boolean',
                'validation_rules' => ['required', 'boolean'],
                'category' => 'payment_reminders',
                'is_active' => true,
            ],

            // Overdue Settings
            [
                'key' => 'overdue_grace_period',
                'name' => 'Overdue Grace Period',
                'description' => 'Number of days after due date before marking as overdue',
                'value' => '3',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:0', 'max:14'],
                'category' => 'overdue',
                'is_active' => true,
            ],
            [
                'key' => 'overdue_notifications_enabled',
                'name' => 'Enable Overdue Notifications',
                'description' => 'Enable automatic overdue payment notifications',
                'value' => '1',
                'type' => 'boolean',
                'validation_rules' => ['required', 'boolean'],
                'category' => 'overdue',
                'is_active' => true,
            ],
            [
                'key' => 'critical_overdue_days',
                'name' => 'Critical Overdue Threshold',
                'description' => 'Number of days overdue before marking as critical priority',
                'value' => '30',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:7', 'max:90'],
                'category' => 'overdue',
                'is_active' => true,
            ],

            // Bad Debt Settings
            [
                'key' => 'bad_debt_threshold_days',
                'name' => 'Bad Debt Threshold',
                'description' => 'Number of days overdue before considering as bad debt',
                'value' => '90',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:30', 'max:365'],
                'category' => 'bad_debt',
                'is_active' => true,
            ],
            [
                'key' => 'bad_debt_notifications_enabled',
                'name' => 'Enable Bad Debt Alerts',
                'description' => 'Enable automatic bad debt alert notifications',
                'value' => '1',
                'type' => 'boolean',
                'validation_rules' => ['required', 'boolean'],
                'category' => 'bad_debt',
                'is_active' => true,
            ],

            // Email Settings
            [
                'key' => 'email_notifications_enabled',
                'name' => 'Enable Email Notifications',
                'description' => 'Enable sending email notifications to borrowers',
                'value' => '1',
                'type' => 'boolean',
                'validation_rules' => ['required', 'boolean'],
                'category' => 'email',
                'is_active' => true,
            ],
            [
                'key' => 'email_daily_limit',
                'name' => 'Daily Email Limit',
                'description' => 'Maximum number of emails to send per day',
                'value' => '100',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:10', 'max:1000'],
                'category' => 'email',
                'is_active' => true,
            ],

            // Notification Frequency
            [
                'key' => 'notification_frequency_hours',
                'name' => 'Notification Check Frequency',
                'description' => 'How often to check for new notifications (in hours)',
                'value' => '24',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:168'],
                'category' => 'general',
                'is_active' => true,
            ],
            [
                'key' => 'duplicate_notification_prevention_hours',
                'name' => 'Duplicate Prevention Period',
                'description' => 'Prevent duplicate notifications within this many hours',
                'value' => '24',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:72'],
                'category' => 'general',
                'is_active' => true,
            ],
        ];

        foreach ($settings as $setting) {
            NotificationSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Notification settings seeded successfully!');
    }
}
