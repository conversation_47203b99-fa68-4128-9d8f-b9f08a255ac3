<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Account;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultAccounts = config('chart_of_accounts.default_accounts');

        foreach ($defaultAccounts as $accountData) {
            Account::updateOrCreate(
                ['code' => $accountData['code']],
                [
                    'name' => $accountData['name'],
                    'type' => $accountData['type'],
                    'parent_id' => $this->getParentId($accountData['parent_code'] ?? null),
                    'is_active' => $accountData['is_active'],
                    'description' => $accountData['description'],
                    'balance' => 0,
                ]
            );
        }
    }

    /**
     * Get parent account ID by code
     */
    private function getParentId(?string $parentCode): ?int
    {
        if (!$parentCode) {
            return null;
        }

        $parent = Account::where('code', $parentCode)->first();
        return $parent ? $parent->id : null;
    }
}
