<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Generating Filament Shield permissions for all resources...');

        // Generate permissions for all Filament resources with admin panel specified
        Artisan::call('shield:generate', [
            '--all' => true,
            '--panel' => 'admin'
        ]);

        $this->command->info('Filament Shield permissions generated successfully!');

        // Assign all permissions to super admin role
        $superAdminRole = \Spatie\Permission\Models\Role::where('name', 'super_admin')->first();
        if ($superAdminRole) {
            $allPermissions = \Spatie\Permission\Models\Permission::all();
            $superAdminRole->syncPermissions($allPermissions);
            $this->command->info('All permissions assigned to super_admin role!');
        }

        // Also assign permissions to admin role
        $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
        if ($adminRole) {
            $allPermissions = \Spatie\Permission\Models\Permission::all();
            $adminRole->syncPermissions($allPermissions);
            $this->command->info('All permissions assigned to admin role!');
        }
    }
}
