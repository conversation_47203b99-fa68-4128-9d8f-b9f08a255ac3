<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // First create all roles
        $this->call(RoleSeeder::class);

        // Then create users
        $superAdmin = \App\Models\User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $superAdmin->assignRole('super_admin');

        $admin = \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $admin->assignRole('admin');
        
        \App\Models\User::factory(5)->create();

        // Finally seed other data
        $this->call([
            AccountSeeder::class,
            LoanTypeSeeder::class,
            BorrowerSeeder::class,
            LoanSeeder::class,
            ReportSeeder::class,
            PermissionSeeder::class,
        ]);
    }
}
