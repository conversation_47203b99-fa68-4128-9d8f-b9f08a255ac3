<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'type',
        'parent_id',
        'is_active',
        'description',
        'balance',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'balance' => 'decimal:2',
    ];

    /**
     * Account types
     */
    const TYPE_ASSET = 'asset';
    const TYPE_LIABILITY = 'liability';
    const TYPE_EQUITY = 'equity';
    const TYPE_INCOME = 'income';
    const TYPE_EXPENSE = 'expense';

    /**
     * Normal balance sides
     */
    const NORMAL_BALANCE_DEBIT = 'debit';
    const NORMAL_BALANCE_CREDIT = 'credit';

    /**
     * Get the normal balance side for this account type
     */
    public function getNormalBalanceAttribute(): string
    {
        $normalBalances = config('chart_of_accounts.normal_balances');
        return $normalBalances[$this->type] ?? self::NORMAL_BALANCE_DEBIT;
    }

    /**
     * Get the parent account
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * Get child accounts
     */
    public function children(): HasMany
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * Get journal entry lines for this account
     */
    public function journalEntryLines(): HasMany
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    /**
     * Get debit journal entry lines
     */
    public function debitLines(): HasMany
    {
        return $this->journalEntryLines()->where('type', 'debit');
    }

    /**
     * Get credit journal entry lines
     */
    public function creditLines(): HasMany
    {
        return $this->journalEntryLines()->where('type', 'credit');
    }

    /**
     * Calculate the current balance
     */
    public function calculateBalance(): float
    {
        $debits = $this->debitLines()->sum('amount');
        $credits = $this->creditLines()->sum('amount');

        // For accounts with normal debit balance (assets, expenses)
        if ($this->normal_balance === self::NORMAL_BALANCE_DEBIT) {
            return $debits - $credits;
        }

        // For accounts with normal credit balance (liabilities, equity, income)
        return $credits - $debits;
    }

    /**
     * Update the balance field with calculated balance
     */
    public function updateBalance(): void
    {
        $this->update(['balance' => $this->calculateBalance()]);
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for accounts by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for asset accounts
     */
    public function scopeAssets($query)
    {
        return $query->ofType(self::TYPE_ASSET);
    }

    /**
     * Scope for liability accounts
     */
    public function scopeLiabilities($query)
    {
        return $query->ofType(self::TYPE_LIABILITY);
    }

    /**
     * Scope for equity accounts
     */
    public function scopeEquity($query)
    {
        return $query->ofType(self::TYPE_EQUITY);
    }

    /**
     * Scope for income accounts
     */
    public function scopeIncome($query)
    {
        return $query->ofType(self::TYPE_INCOME);
    }

    /**
     * Scope for expense accounts
     */
    public function scopeExpenses($query)
    {
        return $query->ofType(self::TYPE_EXPENSE);
    }

    /**
     * Get account hierarchy path
     */
    public function getHierarchyPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * Check if account is a parent account
     */
    public function isParent(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Check if account is a leaf account (no children)
     */
    public function isLeaf(): bool
    {
        return !$this->isParent();
    }

    /**
     * Get all descendant accounts
     */
    public function descendants(): HasMany
    {
        return $this->hasMany(Account::class, 'parent_id')->with('descendants');
    }

    /**
     * Get formatted account code and name
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->code} - {$this->name}";
    }
}
