<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_id',
        'borrower_id',
        'type',
        'status',
        'title',
        'message',
        'amount',
        'due_date',
        'days_overdue',
        'priority',
        'sent_at',
        'read_at',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'due_date' => 'date',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Notification types
    const TYPE_OVERDUE = 'overdue';
    const TYPE_PAYMENT_REMINDER = 'payment_reminder';
    const TYPE_BAD_DEBT = 'bad_debt';
    const TYPE_PAYMENT_DUE = 'payment_due';

    // Notification statuses
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_READ = 'read';
    const STATUS_DISMISSED = 'dismissed';

    // Priority levels
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_CRITICAL = 'critical';

    /**
     * Get the loan that owns the notification
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the borrower that owns the notification
     */
    public function borrower(): BelongsTo
    {
        return $this->belongsTo(Borrower::class);
    }

    /**
     * Mark notification as sent
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): void
    {
        $this->update([
            'status' => self::STATUS_READ,
            'read_at' => now(),
        ]);
    }

    /**
     * Mark notification as dismissed
     */
    public function dismiss(): void
    {
        $this->update(['status' => self::STATUS_DISMISSED]);
    }

    /**
     * Check if notification is overdue
     */
    public function isOverdue(): bool
    {
        return $this->type === self::TYPE_OVERDUE;
    }

    /**
     * Check if notification is for bad debt
     */
    public function isBadDebt(): bool
    {
        return $this->type === self::TYPE_BAD_DEBT;
    }

    /**
     * Check if notification is critical
     */
    public function isCritical(): bool
    {
        return $this->priority === self::PRIORITY_CRITICAL;
    }

    /**
     * Get formatted priority
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            self::PRIORITY_LOW => 'success',
            self::PRIORITY_MEDIUM => 'warning',
            self::PRIORITY_HIGH => 'danger',
            self::PRIORITY_CRITICAL => 'danger',
            default => 'gray',
        };
    }

    /**
     * Scope for pending notifications
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for sent notifications
     */
    public function scopeSent($query)
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->whereIn('status', [self::STATUS_PENDING, self::STATUS_SENT]);
    }

    /**
     * Scope for overdue notifications
     */
    public function scopeOverdue($query)
    {
        return $query->where('type', self::TYPE_OVERDUE);
    }

    /**
     * Scope for bad debt notifications
     */
    public function scopeBadDebt($query)
    {
        return $query->where('type', self::TYPE_BAD_DEBT);
    }

    /**
     * Scope for critical notifications
     */
    public function scopeCritical($query)
    {
        return $query->where('priority', self::PRIORITY_CRITICAL);
    }
}
