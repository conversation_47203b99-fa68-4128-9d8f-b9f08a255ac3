<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;

class JournalEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference_number',
        'date',
        'description',
        'total_amount',
        'is_posted',
        'posted_at',
        'posted_by',
        'source_type',
        'source_id',
        'notes',
    ];

    protected $casts = [
        'date' => 'date',
        'is_posted' => 'boolean',
        'posted_at' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the journal entry lines
     */
    public function lines(): HasMany
    {
        return $this->hasMany(JournalEntryLine::class);
    }

    /**
     * Get debit lines
     */
    public function debitLines(): HasMany
    {
        return $this->lines()->where('type', 'debit');
    }

    /**
     * Get credit lines
     */
    public function creditLines(): HasMany
    {
        return $this->lines()->where('type', 'credit');
    }

    /**
     * Get the user who posted this entry
     */
    public function postedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    /**
     * Get the source model (polymorphic relationship)
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Calculate total debits
     */
    public function getTotalDebitsAttribute(): float
    {
        return $this->debitLines()->sum('amount');
    }

    /**
     * Calculate total credits
     */
    public function getTotalCreditsAttribute(): float
    {
        return $this->creditLines()->sum('amount');
    }

    /**
     * Check if the journal entry is balanced
     */
    public function isBalanced(): bool
    {
        return abs($this->total_debits - $this->total_credits) < 0.01;
    }

    /**
     * Get the difference between debits and credits
     */
    public function getDifferenceAttribute(): float
    {
        return $this->total_debits - $this->total_credits;
    }

    /**
     * Post the journal entry
     */
    public function post(User $user = null): bool
    {
        if ($this->is_posted) {
            return false;
        }

        if (!$this->isBalanced()) {
            throw new \Exception('Journal entry is not balanced. Debits: ' . $this->total_debits . ', Credits: ' . $this->total_credits);
        }

        $this->update([
            'is_posted' => true,
            'posted_at' => now(),
            'posted_by' => $user ? $user->id : auth()->id(),
        ]);

        // Update account balances
        foreach ($this->lines as $line) {
            $line->account->updateBalance();
        }

        return true;
    }

    /**
     * Reverse the journal entry
     */
    public function reverse(string $reason = null): JournalEntry
    {
        if (!$this->is_posted) {
            throw new \Exception('Cannot reverse an unposted journal entry');
        }

        $reversalEntry = static::create([
            'reference_number' => 'REV-' . $this->reference_number,
            'date' => now()->toDateString(),
            'description' => 'Reversal of: ' . $this->description . ($reason ? ' - ' . $reason : ''),
            'total_amount' => $this->total_amount,
            'source_type' => $this->source_type,
            'source_id' => $this->source_id,
            'notes' => 'Reversal of journal entry #' . $this->id,
        ]);

        // Create reversed lines
        foreach ($this->lines as $line) {
            $reversalEntry->lines()->create([
                'account_id' => $line->account_id,
                'type' => $line->type === 'debit' ? 'credit' : 'debit',
                'amount' => $line->amount,
                'description' => 'Reversal: ' . $line->description,
            ]);
        }

        // Post the reversal
        $reversalEntry->post();

        return $reversalEntry;
    }

    /**
     * Scope for posted entries
     */
    public function scopePosted($query)
    {
        return $query->where('is_posted', true);
    }

    /**
     * Scope for unposted entries
     */
    public function scopeUnposted($query)
    {
        return $query->where('is_posted', false);
    }

    /**
     * Scope for entries within date range
     */
    public function scopeBetweenDates($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope for entries by source
     */
    public function scopeForSource($query, $sourceType, $sourceId = null)
    {
        $query = $query->where('source_type', $sourceType);
        
        if ($sourceId) {
            $query = $query->where('source_id', $sourceId);
        }

        return $query;
    }

    /**
     * Generate next reference number
     */
    public static function generateReferenceNumber(): string
    {
        $prefix = 'JE';
        $date = now()->format('Ymd');
        $lastEntry = static::where('reference_number', 'like', $prefix . $date . '%')
            ->orderBy('reference_number', 'desc')
            ->first();

        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->reference_number, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . $date . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Boot method to auto-generate reference number
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($journalEntry) {
            if (empty($journalEntry->reference_number)) {
                $journalEntry->reference_number = static::generateReferenceNumber();
            }
        });
    }
}
