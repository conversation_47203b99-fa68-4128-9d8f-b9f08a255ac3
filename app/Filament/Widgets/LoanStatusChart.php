<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use Filament\Widgets\ChartWidget;

class LoanStatusChart extends ChartWidget
{
    protected static ?int $sort = 3;

    protected static ?string $heading = 'Loan Portfolio Distribution';

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    protected function getData(): array
    {
        $statusCounts = Loan::selectRaw('loan_status, COUNT(*) as count')
            ->groupBy('loan_status')
            ->pluck('count', 'loan_status')
            ->toArray();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($statusCounts as $status => $count) {
            $labels[] = ucfirst($status);
            $data[] = $count;
            
            // Assign colors based on status
            $colors[] = match($status) {
                'approved' => '#10b981', // green
                'pending' => '#f59e0b',   // amber
                'rejected' => '#ef4444', // red
                'completed' => '#3b82f6', // blue
                default => '#6b7280',    // gray
            };
        }

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { 
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ": " + context.parsed + " (" + percentage + "%)";
                        }',
                    ],
                ],
            ],
        ];
    }
}
