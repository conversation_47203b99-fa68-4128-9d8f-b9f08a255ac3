<?php

namespace App\Filament\Widgets;

use App\Models\Borrower;
use App\Models\Loan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class BorrowerStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get borrower statistics
        $totalBorrowers = Borrower::count();
        $activeBorrowers = Borrower::whereHas('loan', function ($query) {
            $query->whereIn('loan_status', ['approved', 'disbursed'])->where('balance', '>', 0);
        })->count();

        $newBorrowersThisMonth = Borrower::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $borrowersWithDefaultedLoans = Borrower::whereHas('loan', function ($query) {
            $query->where('loan_status', 'defaulted');
        })->count();

        return [
            Stat::make('Total Borrowers', Number::format($totalBorrowers, 0))
                ->description('All registered customers')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart([1, 3, 5, 7, 9, 11, 13]),

            Stat::make('Active Borrowers', Number::format($activeBorrowers, 0))
                ->description('With outstanding loans')
                ->descriptionIcon('heroicon-m-user-circle')
                ->color('success')
                ->chart([2, 4, 6, 8, 10, 12, 14]),

            Stat::make('New This Month', Number::format($newBorrowersThisMonth, 0))
                ->description('Recently registered')
                ->descriptionIcon('heroicon-m-user-plus')
                ->color('info')
                ->chart([0, 1, 2, 3, 4, 5, 6]),

            Stat::make('With Defaults', Number::format($borrowersWithDefaultedLoans, 0))
                ->description('Have defaulted loans')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($borrowersWithDefaultedLoans > 0 ? 'danger' : 'success')
                ->chart($borrowersWithDefaultedLoans > 0 ? [1, 2, 3, 4, 5, 6, 7] : [0, 0, 0, 0, 0, 0, 0]),
        ];
    }
}
