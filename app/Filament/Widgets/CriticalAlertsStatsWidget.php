<?php

namespace App\Filament\Widgets;

use App\Models\LoanNotification;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class CriticalAlertsStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get notification counts
        $criticalNotifications = LoanNotification::where('priority', 'critical')
            ->whereIn('status', ['pending', 'sent'])
            ->count();
            
        $overdueNotifications = LoanNotification::where('type', 'overdue')
            ->whereIn('status', ['pending', 'sent'])
            ->count();
            
        $badDebtAlerts = LoanNotification::where('type', 'bad_debt')
            ->whereIn('status', ['pending', 'sent'])
            ->count();
            
        $paymentReminders = LoanNotification::where('type', 'payment_reminder')
            ->whereIn('status', ['pending', 'sent'])
            ->count();

        return [
            Stat::make('Critical Alerts', Number::format($criticalNotifications, 0))
                ->description('Require immediate attention')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($criticalNotifications > 0 ? 'danger' : 'success')
                ->chart($criticalNotifications > 0 ? [1, 2, 3, 5, 8, 13, 21] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('Overdue Payments', Number::format($overdueNotifications, 0))
                ->description('Past due loans')
                ->descriptionIcon('heroicon-m-clock')
                ->color($overdueNotifications > 0 ? 'warning' : 'success')
                ->chart($overdueNotifications > 0 ? [2, 4, 6, 8, 10, 12, 14] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('Bad Debt Alerts', Number::format($badDebtAlerts, 0))
                ->description('Potential write-offs')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color($badDebtAlerts > 0 ? 'danger' : 'success')
                ->chart($badDebtAlerts > 0 ? [1, 1, 2, 3, 5, 8, 13] : [0, 0, 0, 0, 0, 0, 0]),

            Stat::make('Payment Reminders', Number::format($paymentReminders, 0))
                ->description('Upcoming due dates')
                ->descriptionIcon('heroicon-m-bell')
                ->color($paymentReminders > 0 ? 'info' : 'success')
                ->chart($paymentReminders > 0 ? [3, 6, 9, 12, 15, 18, 21] : [0, 0, 0, 0, 0, 0, 0]),
        ];
    }
}
