<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use App\Models\Repayments;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class BusinessOverviewStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get key business metrics
        $totalLoans = Loan::count();
        $activeLoans = Loan::whereIn('loan_status', ['approved', 'disbursed'])->where('balance', '>', 0)->count();
        $totalPortfolioValue = Loan::whereIn('loan_status', ['approved', 'disbursed', 'defaulted'])->sum('principal_amount');
        $outstandingBalance = Loan::whereIn('loan_status', ['approved', 'disbursed', 'defaulted'])->sum('balance');
        $totalRepayments = Repayments::sum('payments');

        // Calculate collection rate
        $collectionRate = $totalPortfolioValue > 0 ? ($totalRepayments / $totalPortfolioValue) * 100 : 0;

        return [
            Stat::make('Total Portfolio Value', '$' . Number::format($totalPortfolioValue, 0))
                ->description('Total loans disbursed')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->url(route('filament.admin.resources.loans.index')),

            Stat::make('Outstanding Balance', '$' . Number::format($outstandingBalance, 0))
                ->description('Amount yet to be collected')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->chart([15, 4, 10, 2, 12, 4, 12])
                ->url(route('filament.admin.resources.loans.index', [
                    'tableFilters[loan_status][value]' => 'disbursed'
                ])),

            Stat::make('Collection Rate', Number::format($collectionRate, 1) . '%')
                ->description('Repayments vs disbursements')
                ->descriptionIcon($collectionRate >= 80 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($collectionRate >= 80 ? 'success' : ($collectionRate >= 60 ? 'warning' : 'danger'))
                ->chart([3, 5, 8, 12, 15, 18, 20])
                ->url(route('filament.admin.resources.repayments.index')),

            Stat::make('Active Loans', Number::format($activeLoans, 0))
                ->description("Out of {$totalLoans} total loans")
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info')
                ->chart([2, 4, 6, 8, 10, 12, 14])
                ->url(route('filament.admin.resources.loans.index', [
                    'tableFilters[loan_status][value]' => 'disbursed'
                ])),
        ];
    }
}
