<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class UpcomingPayments extends BaseWidget
{
    protected static ?int $sort = 7;

    protected static ?string $heading = 'Upcoming Loan Payments';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Loan::query()
                    ->with(['borrower'])
                    ->where('loan_status', 'approved')
                    ->where('balance', '>', 0)
                    ->where(function ($query) {
                        $reminderDays = \App\Models\NotificationSetting::get('payment_reminder_days', 7);
                        $reminderDate = now()->addDays($reminderDays);
                        $query->whereDate('loan_due_date', '<=', $reminderDate)
                              ->whereDate('loan_due_date', '>=', now());
                    })
                    ->limit(15)
            )
            ->columns([
                Tables\Columns\TextColumn::make('loan_number')
                    ->label('Loan #')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('borrower.first_name')
                    ->formatStateUsing(fn ($record) => $record->borrower->first_name . ' ' . $record->borrower->last_name)
                    ->label('Borrower')
                    ->searchable(),
                Tables\Columns\TextColumn::make('loan_due_date')
                    ->date()
                    ->label('Due Date')
                    ->sortable()
                    ->color(function ($state) {
                        $daysUntilDue = now()->diffInDays($state, false);
                        if ($daysUntilDue < 0) return 'danger';
                        if ($daysUntilDue <= 3) return 'warning';
                        return 'success';
                    }),
                Tables\Columns\TextColumn::make('repayment_amount')
                    ->money('USD', divideBy: 1)
                    ->label('Payment Amount')
                    ->sortable(),
                Tables\Columns\TextColumn::make('balance')
                    ->money('USD', divideBy: 1)
                    ->label('Outstanding Balance')
                    ->sortable(),
                Tables\Columns\TextColumn::make('loan_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        'completed' => 'info',
                        default => 'gray',
                    })
                    ->label('Status'),
                Tables\Columns\TextColumn::make('days_until_due')
                    ->getStateUsing(function ($record) {
                        $daysUntilDue = now()->diffInDays(\Carbon\Carbon::parse($record->loan_due_date), false);
                        return $daysUntilDue;
                    })
                    ->color(function ($state) {
                        if ($state < 0) return 'danger';
                        if ($state <= 3) return 'warning';
                        return 'success';
                    })
                    ->formatStateUsing(function ($state) {
                        if ($state < 0) return abs($state) . ' days overdue';
                        if ($state == 0) return 'Due today';
                        return $state . ' days';
                    })
                    ->label('Days Until Due')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('send_reminder')
                    ->icon('heroicon-o-bell')
                    ->color('warning')
                    ->action(function (Loan $record) {
                        // Check if notification already exists for this loan
                        $existingNotification = \App\Models\LoanNotification::where('loan_id', $record->id)
                            ->where('type', 'payment_reminder')
                            ->where('created_at', '>=', now()->subDays(1))
                            ->exists();

                        if (!$existingNotification) {
                            // Create payment reminder for this specific loan
                            \App\Models\LoanNotification::create([
                                'loan_id' => $record->id,
                                'borrower_id' => $record->borrower_id,
                                'type' => 'payment_reminder',
                                'status' => 'pending',
                                'title' => 'Payment Reminder',
                                'message' => "Payment reminder for loan {$record->loan_number}",
                                'amount' => $record->repayment_amount ?? $record->balance,
                                'due_date' => \Carbon\Carbon::parse($record->loan_due_date),
                                'priority' => 'medium',
                            ]);
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Send Payment Reminder')
                    ->modalDescription('This will create a payment reminder notification for this borrower.'),
                Tables\Actions\Action::make('view_loan')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn (Loan $record) => route('filament.admin.resources.loans.view', $record))
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('No Upcoming Payments')
            ->emptyStateDescription('All loan payments are up to date.')
            ->emptyStateIcon('heroicon-o-check-circle')
            ->defaultSort('loan_due_date', 'asc')
            ->poll('60s');
    }
}
