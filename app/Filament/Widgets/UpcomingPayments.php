<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use App\Services\LoanNotificationService;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class UpcomingPayments extends BaseWidget
{
    protected static ?int $sort = 3;

    protected static ?string $heading = 'Upcoming Loan Payments';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        $notificationService = app(LoanNotificationService::class);
        
        return $table
            ->query(
                $notificationService->getUpcomingPayments()
                    ->toQuery()
                    ->with(['borrower'])
                    ->limit(15)
            )
            ->columns([
                Tables\Columns\TextColumn::make('loan_number')
                    ->label('Loan #')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('borrower.first_name')
                    ->formatStateUsing(fn ($record) => $record->borrower->first_name . ' ' . $record->borrower->last_name)
                    ->label('Borrower')
                    ->searchable(),
                Tables\Columns\TextColumn::make('next_payment_date')
                    ->date()
                    ->label('Due Date')
                    ->sortable()
                    ->color(function ($state) {
                        $daysUntilDue = now()->diffInDays($state, false);
                        if ($daysUntilDue < 0) return 'danger';
                        if ($daysUntilDue <= 3) return 'warning';
                        return 'success';
                    }),
                Tables\Columns\TextColumn::make('monthly_payment')
                    ->money('USD', divideBy: 1)
                    ->label('Payment Amount')
                    ->sortable(),
                Tables\Columns\TextColumn::make('balance')
                    ->money('USD', divideBy: 1)
                    ->label('Outstanding Balance')
                    ->sortable(),
                Tables\Columns\TextColumn::make('loan_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        'completed' => 'info',
                        default => 'gray',
                    })
                    ->label('Status'),
                Tables\Columns\TextColumn::make('days_until_due')
                    ->getStateUsing(function ($record) {
                        $daysUntilDue = now()->diffInDays($record->next_payment_date ?? $record->loan_release_date, false);
                        return $daysUntilDue;
                    })
                    ->color(function ($state) {
                        if ($state < 0) return 'danger';
                        if ($state <= 3) return 'warning';
                        return 'success';
                    })
                    ->formatStateUsing(function ($state) {
                        if ($state < 0) return abs($state) . ' days overdue';
                        if ($state == 0) return 'Due today';
                        return $state . ' days';
                    })
                    ->label('Days Until Due')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('send_reminder')
                    ->icon('heroicon-o-bell')
                    ->color('warning')
                    ->action(function (Loan $record) {
                        $notificationService = app(LoanNotificationService::class);
                        // Create a payment reminder notification
                        $notificationService->generatePaymentReminders();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Send Payment Reminder')
                    ->modalDescription('This will create a payment reminder notification for this borrower.'),
                Tables\Actions\Action::make('view_loan')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn (Loan $record) => route('filament.admin.resources.loans.view', $record))
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('No Upcoming Payments')
            ->emptyStateDescription('All loan payments are up to date.')
            ->emptyStateIcon('heroicon-o-check-circle')
            ->defaultSort('next_payment_date', 'asc')
            ->poll('60s');
    }
}
