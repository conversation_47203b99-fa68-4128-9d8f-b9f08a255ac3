<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use App\Models\Repayments;
use Filament\Widgets\ChartWidget;

class LoanPerformanceChart extends ChartWidget
{
    protected static ?int $sort = 2;

    protected static ?string $heading = 'Loan Performance (Last 6 Months)';

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $months = collect();
        $disbursements = collect();
        $repayments = collect();

        // Get last 6 months data
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthName = $month->format('M Y');
            
            $monthlyDisbursements = Loan::where('loan_status', 'approved')
                ->whereYear('loan_release_date', $month->year)
                ->whereMonth('loan_release_date', $month->month)
                ->sum('principal_amount');
                
            $monthlyRepayments = Repayments::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('payments');

            $months->push($monthName);
            $disbursements->push($monthlyDisbursements);
            $repayments->push($monthlyRepayments);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Disbursements',
                    'data' => $disbursements->toArray(),
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Repayments',
                    'data' => $repayments->toArray(),
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $months->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "$" + value.toLocaleString(); }',
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { return context.dataset.label + ": $" + context.parsed.y.toLocaleString(); }',
                    ],
                ],
            ],
        ];
    }
}
