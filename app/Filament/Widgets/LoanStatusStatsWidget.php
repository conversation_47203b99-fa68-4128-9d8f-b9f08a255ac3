<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use App\Models\Borrower;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class LoanStatusStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get loan status counts
        $activeLoans = Loan::whereIn('loan_status', ['approved', 'disbursed'])->count();
        $pendingLoans = Loan::where('loan_status', 'pending')->count();
        $defaultedLoans = Loan::where('loan_status', 'defaulted')->count();
        $repaidLoans = Loan::where('loan_status', 'repaid')->count();
        $totalBorrowers = Borrower::count();

        return [
            Stat::make('Active Loans', Number::format($activeLoans, 0))
                ->description('Currently disbursed loans')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success')
                ->chart([2, 4, 6, 8, 10, 12, 14]),

            Stat::make('Pending Loans', Number::format($pendingLoans, 0))
                ->description('Awaiting approval')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->chart([1, 3, 2, 5, 4, 6, 5]),

            Stat::make('Defaulted Loans', Number::format($defaultedLoans, 0))
                ->description('Loans in default')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger')
                ->chart([0, 1, 1, 2, 3, 4, 6]),

            Stat::make('Fully Paid Loans', Number::format($repaidLoans, 0))
                ->description('Successfully completed')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('info')
                ->chart([1, 2, 4, 6, 8, 10, 12]),

            Stat::make('Total Borrowers', Number::format($totalBorrowers, 0))
                ->description('Registered customers')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart([1, 3, 5, 7, 9, 11, 13]),
        ];
    }
}
