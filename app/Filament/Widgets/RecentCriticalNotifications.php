<?php

namespace App\Filament\Widgets;

use App\Models\LoanNotification;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class RecentCriticalNotifications extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static ?string $heading = 'Critical Loan Notifications';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                LoanNotification::query()
                    ->with(['loan', 'borrower'])
                    ->where('priority', 'critical')
                    ->whereIn('status', ['pending', 'sent'])
                    ->latest()
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('loan.loan_number')
                    ->label('Loan #')
                    ->sortable(),
                Tables\Columns\TextColumn::make('borrower.first_name')
                    ->formatStateUsing(fn ($record) => $record->borrower->first_name . ' ' . $record->borrower->last_name)
                    ->label('Borrower')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'overdue' => 'danger',
                        'bad_debt' => 'danger',
                        'payment_reminder' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'overdue' => 'Overdue',
                        'payment_reminder' => 'Payment Reminder',
                        'bad_debt' => 'Bad Debt',
                        'payment_due' => 'Payment Due',
                        default => $state,
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('title')
                    ->limit(40)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 40) {
                            return null;
                        }
                        return $state;
                    })
                    ->label('Title'),
                Tables\Columns\TextColumn::make('amount')
                    ->money('USD', divideBy: 1)
                    ->label('Amount'),
                Tables\Columns\TextColumn::make('days_overdue')
                    ->numeric()
                    ->color(fn ($state) => $state > 30 ? 'danger' : ($state > 7 ? 'warning' : 'success'))
                    ->label('Days Overdue')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->label('Created')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('mark_as_read')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (LoanNotification $record) {
                        $record->markAsRead();
                    })
                    ->visible(fn (LoanNotification $record) => $record->status !== 'read'),
                Tables\Actions\Action::make('view_loan')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->url(fn (LoanNotification $record) => route('filament.admin.resources.loans.view', $record->loan))
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('No Critical Notifications')
            ->emptyStateDescription('All critical loan notifications have been addressed.')
            ->emptyStateIcon('heroicon-o-check-circle')
            ->poll('30s');
    }
}
