<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class RecentActivityWidget extends BaseWidget
{
    protected static ?int $sort = 4;

    protected static ?string $heading = 'Recent Loans';

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Loan::query()
                    ->with(['borrower'])
                    ->latest()
                    ->limit(8)
            )
            ->columns([
                Tables\Columns\TextColumn::make('loan_number')
                    ->label('Loan #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('borrower.first_name')
                    ->formatStateUsing(fn ($record) => $record->borrower->first_name . ' ' . $record->borrower->last_name)
                    ->label('Borrower')
                    ->searchable(),
                Tables\Columns\TextColumn::make('principal_amount')
                    ->money('USD', divideBy: 1)
                    ->label('Amount')
                    ->sortable(),
                Tables\Columns\TextColumn::make('loan_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        'completed' => 'info',
                        default => 'gray',
                    })
                    ->label('Status'),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->label('Created')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->icon('heroicon-o-eye')
                    ->url(fn (Loan $record) => route('filament.admin.resources.loans.view', $record))
                    ->openUrlInNewTab(),
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated(false)
            ->poll('60s');
    }
}
