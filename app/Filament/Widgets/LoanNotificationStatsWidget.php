<?php

namespace App\Filament\Widgets;

use App\Models\LoanNotification;
use App\Services\LoanNotificationService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LoanNotificationStatsWidget extends BaseWidget
{
    protected static ?int $sort = 5;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $notificationService = app(LoanNotificationService::class);
        $stats = $notificationService->getNotificationStats();

        return [
            Stat::make('Critical Notifications', $stats['critical_notifications'])
                ->description('Urgent attention required')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger')
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[priority][value]' => 'critical'
                ])),

            Stat::make('Overdue Loans', $stats['overdue_loans'])
                ->description('Loans past due date')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[type][value]' => 'overdue'
                ])),

            Stat::make('Bad Debt Alerts', $stats['bad_debt_alerts'])
                ->description('Potential write-offs')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger')
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[type][value]' => 'bad_debt'
                ])),

            Stat::make('Pending Notifications', $stats['pending_notifications'])
                ->description('Awaiting action')
                ->descriptionIcon('heroicon-m-bell')
                ->color('info')
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[status][value]' => 'pending'
                ])),
        ];
    }
}
