<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Services\DoubleEntryService;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Carbon\Carbon;

class TrialBalanceReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static string $view = 'filament.pages.trial-balance-report';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationLabel = 'Trial Balance';

    protected static ?string $title = 'Trial Balance Report';

    public ?array $data = [];

    public array $trialBalanceData = [];

    protected DoubleEntryService $doubleEntryService;

    public function boot(DoubleEntryService $doubleEntryService): void
    {
        $this->doubleEntryService = $doubleEntryService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'as_of_date' => now()->toDateString(),
        ]);

        $this->generateTrialBalance();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('as_of_date')
                    ->label('As of Date')
                    ->default(now())
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn () => $this->generateTrialBalance()),
            ])
            ->statePath('data');
    }

    public function generateTrialBalance(): void
    {
        $asOfDate = $this->data['as_of_date'] ? Carbon::parse($this->data['as_of_date']) : now();
        $this->trialBalanceData = $this->doubleEntryService->getTrialBalance($asOfDate);
    }
}
