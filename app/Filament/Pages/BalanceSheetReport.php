<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Services\DoubleEntryService;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Carbon\Carbon;

class BalanceSheetReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static string $view = 'filament.pages.balance-sheet-report';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationLabel = 'Balance Sheet';

    protected static ?string $title = 'Balance Sheet Report';

    public ?array $data = [];

    public array $balanceSheetData = [];

    protected DoubleEntryService $doubleEntryService;

    public function boot(DoubleEntryService $doubleEntryService): void
    {
        $this->doubleEntryService = $doubleEntryService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'as_of_date' => now()->toDateString(),
        ]);

        $this->generateBalanceSheet();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('as_of_date')
                    ->label('As of Date')
                    ->default(now())
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn () => $this->generateBalanceSheet()),
            ])
            ->statePath('data');
    }

    public function generateBalanceSheet(): void
    {
        $asOfDate = $this->data['as_of_date'] ? Carbon::parse($this->data['as_of_date']) : now();
        $this->balanceSheetData = $this->doubleEntryService->getBalanceSheet($asOfDate);
    }
}
