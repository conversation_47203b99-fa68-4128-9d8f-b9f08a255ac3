<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Services\DoubleEntryService;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Carbon\Carbon;

class IncomeStatementReport extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static string $view = 'filament.pages.income-statement-report';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationLabel = 'Income Statement';

    protected static ?string $title = 'Income Statement Report';

    public ?array $data = [];

    public array $incomeStatementData = [];

    protected DoubleEntryService $doubleEntryService;

    public function boot(DoubleEntryService $doubleEntryService): void
    {
        $this->doubleEntryService = $doubleEntryService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth()->toDateString(),
            'end_date' => now()->endOfMonth()->toDateString(),
        ]);

        $this->generateIncomeStatement();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->default(now()->startOfMonth())
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn () => $this->generateIncomeStatement()),
                DatePicker::make('end_date')
                    ->label('End Date')
                    ->default(now()->endOfMonth())
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn () => $this->generateIncomeStatement()),
            ])
            ->columns(2)
            ->statePath('data');
    }

    public function generateIncomeStatement(): void
    {
        $startDate = $this->data['start_date'] ? Carbon::parse($this->data['start_date']) : now()->startOfMonth();
        $endDate = $this->data['end_date'] ? Carbon::parse($this->data['end_date']) : now()->endOfMonth();
        $this->incomeStatementData = $this->doubleEntryService->getIncomeStatement($startDate, $endDate);
    }
}
