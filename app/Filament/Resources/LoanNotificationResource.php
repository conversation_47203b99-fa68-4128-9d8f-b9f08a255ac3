<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LoanNotificationResource\Pages;
use App\Filament\Resources\LoanNotificationResource\RelationManagers;
use App\Models\LoanNotification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LoanNotificationResource extends Resource
{
    protected static ?string $model = LoanNotification::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell-alert';

    protected static ?string $navigationGroup = 'Loans';

    protected static ?string $navigationLabel = 'Loan Notifications';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('loan_id')
                    ->relationship('loan', 'loan_number')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Loan'),
                Forms\Components\Select::make('borrower_id')
                    ->relationship('borrower', 'first_name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->label('Borrower'),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options([
                        'overdue' => 'Overdue Payment',
                        'payment_reminder' => 'Payment Reminder',
                        'bad_debt' => 'Bad Debt Alert',
                        'payment_due' => 'Payment Due',
                    ])
                    ->label('Notification Type'),
                Forms\Components\Select::make('status')
                    ->required()
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'read' => 'Read',
                        'dismissed' => 'Dismissed',
                    ])
                    ->default('pending')
                    ->label('Status'),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255)
                    ->label('Title'),
                Forms\Components\Textarea::make('message')
                    ->required()
                    ->columnSpanFull()
                    ->label('Message'),
                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->prefix('$')
                    ->label('Amount'),
                Forms\Components\DatePicker::make('due_date')
                    ->label('Due Date'),
                Forms\Components\TextInput::make('days_overdue')
                    ->numeric()
                    ->label('Days Overdue'),
                Forms\Components\Select::make('priority')
                    ->required()
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'critical' => 'Critical',
                    ])
                    ->default('medium')
                    ->label('Priority'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('loan.loan_number')
                    ->sortable()
                    ->searchable()
                    ->label('Loan Number'),
                Tables\Columns\TextColumn::make('borrower.first_name')
                    ->formatStateUsing(fn ($record) => $record->borrower->first_name . ' ' . $record->borrower->last_name)
                    ->sortable()
                    ->searchable()
                    ->label('Borrower'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'overdue' => 'danger',
                        'payment_reminder' => 'warning',
                        'bad_debt' => 'danger',
                        'payment_due' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'overdue' => 'Overdue',
                        'payment_reminder' => 'Payment Reminder',
                        'bad_debt' => 'Bad Debt',
                        'payment_due' => 'Payment Due',
                        default => $state,
                    })
                    ->label('Type'),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'sent' => 'info',
                        'read' => 'success',
                        'dismissed' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state))
                    ->label('Status'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->label('Title'),
                Tables\Columns\TextColumn::make('amount')
                    ->money('USD', divideBy: 1)
                    ->sortable()
                    ->label('Amount'),
                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'low' => 'success',
                        'medium' => 'warning',
                        'high' => 'danger',
                        'critical' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state))
                    ->label('Priority'),
                Tables\Columns\TextColumn::make('days_overdue')
                    ->numeric()
                    ->sortable()
                    ->color(fn ($state) => $state > 30 ? 'danger' : ($state > 7 ? 'warning' : 'success'))
                    ->label('Days Overdue'),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable()
                    ->label('Due Date'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Created'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'overdue' => 'Overdue Payment',
                        'payment_reminder' => 'Payment Reminder',
                        'bad_debt' => 'Bad Debt Alert',
                        'payment_due' => 'Payment Due',
                    ])
                    ->label('Notification Type'),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'sent' => 'Sent',
                        'read' => 'Read',
                        'dismissed' => 'Dismissed',
                    ])
                    ->label('Status'),
                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'critical' => 'Critical',
                    ])
                    ->label('Priority'),
                Tables\Filters\Filter::make('overdue_only')
                    ->query(fn ($query) => $query->where('days_overdue', '>', 0))
                    ->label('Overdue Only'),
                Tables\Filters\Filter::make('critical_only')
                    ->query(fn ($query) => $query->where('priority', 'critical'))
                    ->label('Critical Only'),
            ])
            ->actions([
                Tables\Actions\Action::make('mark_as_read')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (LoanNotification $record) {
                        $record->markAsRead();
                    })
                    ->visible(fn (LoanNotification $record) => $record->status !== 'read'),
                Tables\Actions\Action::make('dismiss')
                    ->icon('heroicon-o-x-mark')
                    ->color('gray')
                    ->action(function (LoanNotification $record) {
                        $record->dismiss();
                    })
                    ->visible(fn (LoanNotification $record) => $record->status !== 'dismissed'),
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_as_read')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->markAsRead();
                            }
                        }),
                    Tables\Actions\BulkAction::make('dismiss')
                        ->icon('heroicon-o-x-mark')
                        ->color('gray')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->dismiss();
                            }
                        }),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLoanNotifications::route('/'),
            'create' => Pages\CreateLoanNotification::route('/create'),
            'edit' => Pages\EditLoanNotification::route('/{record}/edit'),
        ];
    }
}
