<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NotificationSettingResource\Pages;
use App\Filament\Resources\NotificationSettingResource\RelationManagers;
use App\Models\NotificationSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class NotificationSettingResource extends Resource
{
    protected static ?string $model = NotificationSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?string $navigationLabel = 'Notification Settings';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->label('Setting Key')
                            ->helperText('Unique identifier for this setting'),
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Display Name'),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull()
                            ->label('Description')
                            ->helperText('Explain what this setting controls'),
                        Forms\Components\Select::make('category')
                            ->required()
                            ->options([
                                'payment_reminders' => 'Payment Reminders',
                                'overdue' => 'Overdue Notifications',
                                'bad_debt' => 'Bad Debt Alerts',
                                'email' => 'Email Settings',
                                'general' => 'General Settings',
                            ])
                            ->default('general')
                            ->label('Category'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Value Configuration')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->required()
                            ->options([
                                'string' => 'Text',
                                'integer' => 'Number',
                                'decimal' => 'Decimal',
                                'boolean' => 'Yes/No',
                                'array' => 'List',
                                'json' => 'JSON',
                            ])
                            ->default('integer')
                            ->live()
                            ->label('Value Type'),
                        Forms\Components\TextInput::make('value')
                            ->required()
                            ->label('Value')
                            ->helperText(fn ($get) => match($get('type')) {
                                'boolean' => 'Enter 1 for Yes, 0 for No',
                                'integer' => 'Enter a whole number',
                                'decimal' => 'Enter a decimal number',
                                'array', 'json' => 'Enter valid JSON format',
                                default => 'Enter the setting value',
                            }),
                        Forms\Components\Toggle::make('is_active')
                            ->required()
                            ->default(true)
                            ->label('Active')
                            ->helperText('Inactive settings will not be used'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Setting Name'),
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->sortable()
                    ->label('Key')
                    ->fontFamily('mono')
                    ->copyable(),
                Tables\Columns\TextColumn::make('category')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'payment_reminders' => 'info',
                        'overdue' => 'warning',
                        'bad_debt' => 'danger',
                        'email' => 'success',
                        'general' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'payment_reminders' => 'Payment Reminders',
                        'overdue' => 'Overdue',
                        'bad_debt' => 'Bad Debt',
                        'email' => 'Email',
                        'general' => 'General',
                        default => ucfirst($state),
                    })
                    ->sortable()
                    ->label('Category'),
                Tables\Columns\TextColumn::make('value')
                    ->formatStateUsing(function ($record) {
                        return $record->formatted_value;
                    })
                    ->label('Current Value')
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color('gray')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'string' => 'Text',
                        'integer' => 'Number',
                        'decimal' => 'Decimal',
                        'boolean' => 'Yes/No',
                        'array' => 'List',
                        'json' => 'JSON',
                        default => ucfirst($state),
                    })
                    ->label('Type'),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->label('Description')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'payment_reminders' => 'Payment Reminders',
                        'overdue' => 'Overdue Notifications',
                        'bad_debt' => 'Bad Debt Alerts',
                        'email' => 'Email Settings',
                        'general' => 'General Settings',
                    ])
                    ->label('Category'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'string' => 'Text',
                        'integer' => 'Number',
                        'decimal' => 'Decimal',
                        'boolean' => 'Yes/No',
                        'array' => 'List',
                        'json' => 'JSON',
                    ])
                    ->label('Type'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationSettings::route('/'),
            'create' => Pages\CreateNotificationSetting::route('/create'),
            'edit' => Pages\EditNotificationSetting::route('/{record}/edit'),
        ];
    }
}
