<?php

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use App\Services\DoubleEntryService;
use Filament\Resources\Pages\Page;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Carbon\Carbon;

class TrialBalance extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = AccountResource::class;

    protected static string $view = 'filament.resources.account-resource.pages.trial-balance';

    protected static ?string $navigationLabel = 'Trial Balance';

    protected static ?string $title = 'Trial Balance';

    public ?array $data = [];

    public array $trialBalanceData = [];

    protected DoubleEntryService $doubleEntryService;

    public function boot(DoubleEntryService $doubleEntryService): void
    {
        $this->doubleEntryService = $doubleEntryService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'as_of_date' => now()->toDateString(),
        ]);

        $this->generateTrialBalance();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('as_of_date')
                    ->label('As of Date')
                    ->default(now())
                    ->required()
                    ->live()
                    ->afterStateUpdated(fn () => $this->generateTrialBalance()),
            ])
            ->statePath('data');
    }

    public function generateTrialBalance(): void
    {
        $asOfDate = $this->data['as_of_date'] ? Carbon::parse($this->data['as_of_date']) : now();
        $this->trialBalanceData = $this->doubleEntryService->getTrialBalance($asOfDate);
    }
}
