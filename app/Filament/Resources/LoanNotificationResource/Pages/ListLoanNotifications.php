<?php

namespace App\Filament\Resources\LoanNotificationResource\Pages;

use App\Filament\Resources\LoanNotificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLoanNotifications extends ListRecords
{
    protected static string $resource = LoanNotificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\CriticalAlertsStatsWidget::class,
        ];
    }
}
