<?php

namespace App\Services;

use App\Models\Loan;
use App\Models\Repayments;
use App\Models\JournalEntry;
use App\Services\DoubleEntryService;
use Illuminate\Support\Facades\Log;

class LoanAccountingService
{
    protected DoubleEntryService $doubleEntryService;

    public function __construct(DoubleEntryService $doubleEntryService)
    {
        $this->doubleEntryService = $doubleEntryService;
    }

    /**
     * Record loan disbursement in double entry system
     */
    public function recordLoanDisbursement(Loan $loan): ?JournalEntry
    {
        try {
            // Only record if loan is approved
            if ($loan->loan_status !== 'approved') {
                return null;
            }

            $journalEntry = $this->doubleEntryService->recordLoanDisbursement(
                amount: $loan->principal_amount,
                bankAccountCode: '1010', // Cash in Bank - Main Account
                loanReceivableCode: '1100', // Loans Receivable
                options: [
                    'description' => "Loan disbursement - {$loan->loan_number}",
                    'date' => $loan->loan_release_date,
                    'source_type' => Loan::class,
                    'source_id' => $loan->id,
                    'notes' => "Loan disbursed to {$loan->borrower->first_name} {$loan->borrower->last_name}",
                ]
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for loan disbursement: {$loan->loan_number}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for loan disbursement: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Record loan repayment in double entry system
     */
    public function recordLoanRepayment(Repayments $repayment): ?JournalEntry
    {
        try {
            $loan = $repayment->loan_number;
            
            // Calculate principal and interest portions
            $totalPayment = $repayment->payments;
            $principalAmount = $totalPayment; // For now, treating all as principal
            $interestAmount = 0; // You can enhance this to calculate interest portion

            $journalEntry = $this->doubleEntryService->recordLoanRepayment(
                principalAmount: $principalAmount,
                interestAmount: $interestAmount,
                bankAccountCode: '1010', // Cash in Bank - Main Account
                loanReceivableCode: '1100', // Loans Receivable
                interestIncomeCode: '4000', // Interest Income
                options: [
                    'description' => "Loan repayment - {$loan->loan_number}",
                    'date' => now()->toDateString(),
                    'source_type' => Repayments::class,
                    'source_id' => $repayment->id,
                    'notes' => "Payment method: {$repayment->payments_method}",
                ]
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for loan repayment: {$loan->loan_number}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for loan repayment: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Record expense in double entry system
     */
    public function recordExpense(
        string $expenseAccountCode,
        float $amount,
        string $description,
        string $paymentMethod = 'cash',
        array $options = []
    ): ?JournalEntry {
        try {
            // Determine payment account based on method
            $paymentAccountCode = match($paymentMethod) {
                'bank', 'transfer' => '1010', // Cash in Bank - Main Account
                'cash' => '1000', // Cash in Hand
                default => '1010',
            };

            $journalEntry = $this->doubleEntryService->recordExpense(
                expenseAccountCode: $expenseAccountCode,
                amount: $amount,
                description: $description,
                paymentAccountCode: $paymentAccountCode,
                options: $options
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for expense: {$description}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for expense: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Record processing fee income
     */
    public function recordProcessingFee(Loan $loan, float $feeAmount): ?JournalEntry
    {
        try {
            $journalEntry = $this->doubleEntryService->recordIncome(
                incomeAccountCode: '4100', // Processing Fees
                amount: $feeAmount,
                description: "Processing fee - {$loan->loan_number}",
                receivingAccountCode: '1010', // Cash in Bank - Main Account
                options: [
                    'date' => $loan->loan_release_date,
                    'source_type' => Loan::class,
                    'source_id' => $loan->id,
                    'notes' => "Processing fee for loan {$loan->loan_number}",
                ]
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for processing fee: {$loan->loan_number}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for processing fee: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Record late payment fee
     */
    public function recordLateFee(Loan $loan, float $feeAmount): ?JournalEntry
    {
        try {
            $journalEntry = $this->doubleEntryService->recordIncome(
                incomeAccountCode: '4200', // Late Payment Fees
                amount: $feeAmount,
                description: "Late payment fee - {$loan->loan_number}",
                receivingAccountCode: '1010', // Cash in Bank - Main Account
                options: [
                    'date' => now()->toDateString(),
                    'source_type' => Loan::class,
                    'source_id' => $loan->id,
                    'notes' => "Late payment fee for loan {$loan->loan_number}",
                ]
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for late fee: {$loan->loan_number}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for late fee: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Record bad debt write-off
     */
    public function recordBadDebtWriteOff(Loan $loan, float $amount): ?JournalEntry
    {
        try {
            $journalEntry = $this->doubleEntryService->createSimpleEntry(
                debitAccountCode: '5400', // Bad Debt Expense
                creditAccountCode: '1100', // Loans Receivable
                amount: $amount,
                description: "Bad debt write-off - {$loan->loan_number}",
                options: [
                    'date' => now()->toDateString(),
                    'source_type' => Loan::class,
                    'source_id' => $loan->id,
                    'notes' => "Write-off of uncollectible loan {$loan->loan_number}",
                ]
            );

            // Post the journal entry immediately
            $this->doubleEntryService->postJournalEntry($journalEntry);

            Log::info("Double entry recorded for bad debt write-off: {$loan->loan_number}");

            return $journalEntry;

        } catch (\Exception $e) {
            Log::error("Failed to record double entry for bad debt write-off: {$e->getMessage()}");
            return null;
        }
    }
}
