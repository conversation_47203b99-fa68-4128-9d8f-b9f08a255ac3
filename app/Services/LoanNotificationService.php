<?php

namespace App\Services;

use App\Models\Loan;
use App\Models\LoanNotification;
use App\Models\Borrower;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class LoanNotificationService
{
    // Configuration constants - these could be moved to config file
    const PAYMENT_REMINDER_DAYS = 7; // Days before due date to send reminder
    const OVERDUE_GRACE_PERIOD = 3; // Days after due date before marking overdue
    const BAD_DEBT_THRESHOLD_DAYS = 90; // Days overdue before considering bad debt
    const CRITICAL_OVERDUE_DAYS = 30; // Days overdue before marking as critical

    /**
     * Generate all loan notifications
     */
    public function generateAllNotifications(): array
    {
        $results = [
            'payment_reminders' => $this->generatePaymentReminders(),
            'overdue_notifications' => $this->generateOverdueNotifications(),
            'bad_debt_alerts' => $this->generateBadDebtAlerts(),
        ];

        Log::info('Generated loan notifications', $results);

        return $results;
    }

    /**
     * Generate payment reminder notifications
     */
    public function generatePaymentReminders(): int
    {
        $upcomingLoans = $this->getUpcomingPayments();
        $count = 0;

        foreach ($upcomingLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_PAYMENT_REMINDER)) {
                $this->createPaymentReminderNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Generate overdue payment notifications
     */
    public function generateOverdueNotifications(): int
    {
        $overdueLoans = $this->getOverdueLoans();
        $count = 0;

        foreach ($overdueLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_OVERDUE)) {
                $this->createOverdueNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Generate bad debt alerts
     */
    public function generateBadDebtAlerts(): int
    {
        $badDebtLoans = $this->getBadDebtLoans();
        $count = 0;

        foreach ($badDebtLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_BAD_DEBT)) {
                $this->createBadDebtNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get loans with upcoming payments
     */
    public function getUpcomingPayments(): Collection
    {
        $reminderDate = now()->addDays(self::PAYMENT_REMINDER_DAYS);

        return Loan::with('borrower')
            ->where('loan_status', 'approved')
            ->where('balance', '>', 0)
            ->where(function ($query) use ($reminderDate) {
                // Check if next payment is due within reminder period
                $query->whereDate('next_payment_date', '<=', $reminderDate)
                      ->whereDate('next_payment_date', '>=', now());
            })
            ->get();
    }

    /**
     * Get overdue loans
     */
    public function getOverdueLoans(): Collection
    {
        $graceDate = now()->subDays(self::OVERDUE_GRACE_PERIOD);

        return Loan::with('borrower')
            ->where('loan_status', 'approved')
            ->where('balance', '>', 0)
            ->where(function ($query) use ($graceDate) {
                $query->whereDate('next_payment_date', '<', $graceDate)
                      ->orWhere(function ($q) use ($graceDate) {
                          // Also check loans where last payment was due before grace period
                          $q->whereDate('loan_release_date', '<', $graceDate)
                            ->whereNull('next_payment_date');
                      });
            })
            ->get();
    }

    /**
     * Get loans that should be considered bad debt
     */
    public function getBadDebtLoans(): Collection
    {
        $badDebtDate = now()->subDays(self::BAD_DEBT_THRESHOLD_DAYS);

        return Loan::with('borrower')
            ->where('loan_status', 'approved')
            ->where('balance', '>', 0)
            ->where(function ($query) use ($badDebtDate) {
                $query->whereDate('next_payment_date', '<', $badDebtDate)
                      ->orWhere(function ($q) use ($badDebtDate) {
                          $q->whereDate('loan_release_date', '<', $badDebtDate)
                            ->whereNull('next_payment_date');
                      });
            })
            ->get();
    }

    /**
     * Create payment reminder notification
     */
    protected function createPaymentReminderNotification(Loan $loan): LoanNotification
    {
        $daysUntilDue = now()->diffInDays($loan->next_payment_date ?? $loan->loan_release_date, false);
        
        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_PAYMENT_REMINDER,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Payment Reminder',
            'message' => "Payment of $" . number_format($loan->monthly_payment ?? $loan->balance, 2) . 
                        " is due in {$daysUntilDue} days for loan {$loan->loan_number}",
            'amount' => $loan->monthly_payment ?? $loan->balance,
            'due_date' => $loan->next_payment_date,
            'priority' => LoanNotification::PRIORITY_MEDIUM,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'days_until_due' => $daysUntilDue,
            ],
        ]);
    }

    /**
     * Create overdue notification
     */
    protected function createOverdueNotification(Loan $loan): LoanNotification
    {
        $daysOverdue = $this->calculateDaysOverdue($loan);
        $priority = $daysOverdue >= self::CRITICAL_OVERDUE_DAYS ? 
                   LoanNotification::PRIORITY_CRITICAL : 
                   LoanNotification::PRIORITY_HIGH;

        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_OVERDUE,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Overdue Payment',
            'message' => "Payment of $" . number_format($loan->balance, 2) . 
                        " is {$daysOverdue} days overdue for loan {$loan->loan_number}",
            'amount' => $loan->balance,
            'due_date' => $loan->next_payment_date,
            'days_overdue' => $daysOverdue,
            'priority' => $priority,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'original_amount' => $loan->principal_amount,
                'current_balance' => $loan->balance,
            ],
        ]);
    }

    /**
     * Create bad debt notification
     */
    protected function createBadDebtNotification(Loan $loan): LoanNotification
    {
        $daysOverdue = $this->calculateDaysOverdue($loan);

        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_BAD_DEBT,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Bad Debt Alert',
            'message' => "Loan {$loan->loan_number} with balance $" . number_format($loan->balance, 2) . 
                        " is {$daysOverdue} days overdue and should be considered for bad debt write-off",
            'amount' => $loan->balance,
            'due_date' => $loan->next_payment_date,
            'days_overdue' => $daysOverdue,
            'priority' => LoanNotification::PRIORITY_CRITICAL,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'borrower_name' => $loan->borrower->first_name . ' ' . $loan->borrower->last_name,
                'original_amount' => $loan->principal_amount,
                'current_balance' => $loan->balance,
                'suggested_action' => 'Consider bad debt write-off',
            ],
        ]);
    }

    /**
     * Calculate days overdue for a loan
     */
    protected function calculateDaysOverdue(Loan $loan): int
    {
        $dueDate = $loan->next_payment_date ?? $loan->loan_release_date;
        return now()->diffInDays($dueDate, false);
    }

    /**
     * Check if loan has recent notification of given type
     */
    protected function hasRecentNotification(Loan $loan, string $type): bool
    {
        return LoanNotification::where('loan_id', $loan->id)
            ->where('type', $type)
            ->where('created_at', '>=', now()->subDays(1)) // Don't create duplicate notifications within 24 hours
            ->exists();
    }

    /**
     * Get notification statistics
     */
    public function getNotificationStats(): array
    {
        return [
            'total_notifications' => LoanNotification::count(),
            'pending_notifications' => LoanNotification::pending()->count(),
            'overdue_loans' => LoanNotification::overdue()->unread()->count(),
            'bad_debt_alerts' => LoanNotification::badDebt()->unread()->count(),
            'critical_notifications' => LoanNotification::critical()->unread()->count(),
        ];
    }

    /**
     * Mark all notifications as read for a specific loan
     */
    public function markLoanNotificationsAsRead(Loan $loan): int
    {
        return LoanNotification::where('loan_id', $loan->id)
            ->unread()
            ->update([
                'status' => LoanNotification::STATUS_READ,
                'read_at' => now(),
            ]);
    }
}
