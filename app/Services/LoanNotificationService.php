<?php

namespace App\Services;

use App\Models\Loan;
use App\Models\LoanNotification;
use App\Models\Borrower;
use App\Models\NotificationSetting;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class LoanNotificationService
{
    /**
     * Get payment reminder days from settings
     */
    protected function getPaymentReminderDays(): int
    {
        return NotificationSetting::get('payment_reminder_days', 7);
    }

    /**
     * Get overdue grace period from settings
     */
    protected function getOverdueGracePeriod(): int
    {
        return NotificationSetting::get('overdue_grace_period', 3);
    }

    /**
     * Get bad debt threshold days from settings
     */
    protected function getBadDebtThresholdDays(): int
    {
        return NotificationSetting::get('bad_debt_threshold_days', 90);
    }

    /**
     * Get critical overdue days from settings
     */
    protected function getCriticalOverdueDays(): int
    {
        return NotificationSetting::get('critical_overdue_days', 30);
    }

    /**
     * Check if notifications are enabled
     */
    protected function areNotificationsEnabled(string $type): bool
    {
        return match($type) {
            'payment_reminder' => NotificationSetting::get('payment_reminder_enabled', true),
            'overdue' => NotificationSetting::get('overdue_notifications_enabled', true),
            'bad_debt' => NotificationSetting::get('bad_debt_notifications_enabled', true),
            'email' => NotificationSetting::get('email_notifications_enabled', true),
            default => true,
        };
    }

    /**
     * Generate all loan notifications
     */
    public function generateAllNotifications(): array
    {
        $results = [
            'payment_reminders' => $this->generatePaymentReminders(),
            'overdue_notifications' => $this->generateOverdueNotifications(),
            'bad_debt_alerts' => $this->generateBadDebtAlerts(),
        ];

        Log::info('Generated loan notifications', $results);

        return $results;
    }

    /**
     * Generate payment reminder notifications
     */
    public function generatePaymentReminders(): int
    {
        $upcomingLoans = $this->getUpcomingPayments();
        $count = 0;

        foreach ($upcomingLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_PAYMENT_REMINDER)) {
                $this->createPaymentReminderNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Generate overdue payment notifications
     */
    public function generateOverdueNotifications(): int
    {
        $overdueLoans = $this->getOverdueLoans();
        $count = 0;

        foreach ($overdueLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_OVERDUE)) {
                $this->createOverdueNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Generate bad debt alerts
     */
    public function generateBadDebtAlerts(): int
    {
        $badDebtLoans = $this->getBadDebtLoans();
        $count = 0;

        foreach ($badDebtLoans as $loan) {
            if (!$this->hasRecentNotification($loan, LoanNotification::TYPE_BAD_DEBT)) {
                $this->createBadDebtNotification($loan);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get loans with upcoming payments
     */
    public function getUpcomingPayments(): Collection
    {
        $reminderDate = now()->addDays($this->getPaymentReminderDays());

        return Loan::with('borrower')
            ->whereIn('loan_status', ['approved', 'disbursed'])
            ->where('balance', '>', 0)
            ->where(function ($query) use ($reminderDate) {
                // Check if loan due date is within reminder period
                $query->whereDate('loan_due_date', '<=', $reminderDate)
                      ->whereDate('loan_due_date', '>=', now());
            })
            ->get();
    }

    /**
     * Get overdue loans
     */
    public function getOverdueLoans(): Collection
    {
        $graceDate = now()->subDays($this->getOverdueGracePeriod());

        return Loan::with('borrower')
            ->where('loan_status', 'approved')
            ->where('balance', '>', 0)
            ->whereDate('loan_due_date', '<', $graceDate)
            ->get();
    }

    /**
     * Get loans that should be considered bad debt
     */
    public function getBadDebtLoans(): Collection
    {
        $badDebtDate = now()->subDays($this->getBadDebtThresholdDays());

        return Loan::with('borrower')
            ->where('loan_status', 'approved')
            ->where('balance', '>', 0)
            ->whereDate('loan_due_date', '<', $badDebtDate)
            ->get();
    }

    /**
     * Create payment reminder notification
     */
    protected function createPaymentReminderNotification(Loan $loan): LoanNotification
    {
        $dueDate = \Carbon\Carbon::parse($loan->loan_due_date);
        $daysUntilDue = now()->diffInDays($dueDate, false);

        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_PAYMENT_REMINDER,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Payment Reminder',
            'message' => "Payment of $" . number_format($loan->repayment_amount ?? $loan->balance, 2) .
                        " is due in {$daysUntilDue} days for loan {$loan->loan_number}",
            'amount' => $loan->repayment_amount ?? $loan->balance,
            'due_date' => $dueDate,
            'priority' => LoanNotification::PRIORITY_MEDIUM,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'days_until_due' => $daysUntilDue,
            ],
        ]);
    }

    /**
     * Create overdue notification
     */
    protected function createOverdueNotification(Loan $loan): LoanNotification
    {
        $daysOverdue = $this->calculateDaysOverdue($loan);
        $priority = $daysOverdue >= $this->getCriticalOverdueDays() ?
                   LoanNotification::PRIORITY_CRITICAL :
                   LoanNotification::PRIORITY_HIGH;

        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_OVERDUE,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Overdue Payment',
            'message' => "Payment of $" . number_format($loan->balance, 2) .
                        " is {$daysOverdue} days overdue for loan {$loan->loan_number}",
            'amount' => $loan->balance,
            'due_date' => \Carbon\Carbon::parse($loan->loan_due_date),
            'days_overdue' => $daysOverdue,
            'priority' => $priority,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'original_amount' => $loan->principal_amount,
                'current_balance' => $loan->balance,
            ],
        ]);
    }

    /**
     * Create bad debt notification
     */
    protected function createBadDebtNotification(Loan $loan): LoanNotification
    {
        $daysOverdue = $this->calculateDaysOverdue($loan);

        return LoanNotification::create([
            'loan_id' => $loan->id,
            'borrower_id' => $loan->borrower_id,
            'type' => LoanNotification::TYPE_BAD_DEBT,
            'status' => LoanNotification::STATUS_PENDING,
            'title' => 'Bad Debt Alert',
            'message' => "Loan {$loan->loan_number} with balance $" . number_format($loan->balance, 2) .
                        " is {$daysOverdue} days overdue and should be considered for bad debt write-off",
            'amount' => $loan->balance,
            'due_date' => \Carbon\Carbon::parse($loan->loan_due_date),
            'days_overdue' => $daysOverdue,
            'priority' => LoanNotification::PRIORITY_CRITICAL,
            'metadata' => [
                'loan_number' => $loan->loan_number,
                'borrower_name' => $loan->borrower->first_name . ' ' . $loan->borrower->last_name,
                'original_amount' => $loan->principal_amount,
                'current_balance' => $loan->balance,
                'suggested_action' => 'Consider bad debt write-off',
            ],
        ]);
    }

    /**
     * Calculate days overdue for a loan
     */
    protected function calculateDaysOverdue(Loan $loan): int
    {
        $dueDate = \Carbon\Carbon::parse($loan->loan_due_date);
        return now()->diffInDays($dueDate, false);
    }

    /**
     * Check if loan has recent notification of given type
     */
    protected function hasRecentNotification(Loan $loan, string $type): bool
    {
        return LoanNotification::where('loan_id', $loan->id)
            ->where('type', $type)
            ->where('created_at', '>=', now()->subDays(1)) // Don't create duplicate notifications within 24 hours
            ->exists();
    }

    /**
     * Get notification statistics
     */
    public function getNotificationStats(): array
    {
        return [
            'total_notifications' => LoanNotification::count(),
            'pending_notifications' => LoanNotification::pending()->count(),
            'overdue_loans' => LoanNotification::overdue()->unread()->count(),
            'bad_debt_alerts' => LoanNotification::badDebt()->unread()->count(),
            'critical_notifications' => LoanNotification::critical()->unread()->count(),
        ];
    }

    /**
     * Mark all notifications as read for a specific loan
     */
    public function markLoanNotificationsAsRead(Loan $loan): int
    {
        return LoanNotification::where('loan_id', $loan->id)
            ->unread()
            ->update([
                'status' => LoanNotification::STATUS_READ,
                'read_at' => now(),
            ]);
    }
}
