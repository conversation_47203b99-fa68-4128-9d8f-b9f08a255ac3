<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\JournalEntryLine;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DoubleEntryService
{
    /**
     * Create a journal entry with multiple lines
     */
    public function createJournalEntry(array $data): JournalEntry
    {
        return DB::transaction(function () use ($data) {
            // Validate required fields
            $this->validateJournalEntryData($data);

            // Create the journal entry
            $journalEntry = JournalEntry::create([
                'reference_number' => $data['reference_number'] ?? null,
                'date' => $data['date'] ?? now()->toDateString(),
                'description' => $data['description'],
                'total_amount' => $data['total_amount'] ?? 0,
                'source_type' => $data['source_type'] ?? null,
                'source_id' => $data['source_id'] ?? null,
                'notes' => $data['notes'] ?? null,
            ]);

            // Create journal entry lines
            $totalDebits = 0;
            $totalCredits = 0;

            foreach ($data['lines'] as $lineData) {
                $line = $this->createJournalEntryLine($journalEntry, $lineData);
                
                if ($line->isDebit()) {
                    $totalDebits += $line->amount;
                } else {
                    $totalCredits += $line->amount;
                }
            }

            // Update total amount if not provided
            if (!isset($data['total_amount'])) {
                $journalEntry->update(['total_amount' => max($totalDebits, $totalCredits)]);
            }

            // Validate that debits equal credits
            if (abs($totalDebits - $totalCredits) > 0.01) {
                throw new \Exception("Journal entry is not balanced. Debits: {$totalDebits}, Credits: {$totalCredits}");
            }

            return $journalEntry;
        });
    }

    /**
     * Create a simple two-line journal entry (debit one account, credit another)
     */
    public function createSimpleEntry(
        string $debitAccountCode,
        string $creditAccountCode,
        float $amount,
        string $description,
        array $options = []
    ): JournalEntry {
        $debitAccount = Account::where('code', $debitAccountCode)->firstOrFail();
        $creditAccount = Account::where('code', $creditAccountCode)->firstOrFail();

        return $this->createJournalEntry([
            'description' => $description,
            'date' => $options['date'] ?? now()->toDateString(),
            'source_type' => $options['source_type'] ?? null,
            'source_id' => $options['source_id'] ?? null,
            'notes' => $options['notes'] ?? null,
            'lines' => [
                [
                    'account_id' => $debitAccount->id,
                    'type' => 'debit',
                    'amount' => $amount,
                    'description' => $description,
                ],
                [
                    'account_id' => $creditAccount->id,
                    'type' => 'credit',
                    'amount' => $amount,
                    'description' => $description,
                ],
            ],
        ]);
    }

    /**
     * Record loan disbursement
     */
    public function recordLoanDisbursement(
        float $amount,
        string $bankAccountCode = '1010',
        string $loanReceivableCode = '1100',
        array $options = []
    ): JournalEntry {
        return $this->createSimpleEntry(
            $loanReceivableCode,
            $bankAccountCode,
            $amount,
            $options['description'] ?? 'Loan disbursement',
            $options
        );
    }

    /**
     * Record loan repayment
     */
    public function recordLoanRepayment(
        float $principalAmount,
        float $interestAmount = 0,
        string $bankAccountCode = '1010',
        string $loanReceivableCode = '1100',
        string $interestIncomeCode = '4000',
        array $options = []
    ): JournalEntry {
        $lines = [
            [
                'account_id' => Account::where('code', $bankAccountCode)->firstOrFail()->id,
                'type' => 'debit',
                'amount' => $principalAmount + $interestAmount,
                'description' => 'Loan repayment received',
            ],
            [
                'account_id' => Account::where('code', $loanReceivableCode)->firstOrFail()->id,
                'type' => 'credit',
                'amount' => $principalAmount,
                'description' => 'Principal repayment',
            ],
        ];

        if ($interestAmount > 0) {
            $lines[] = [
                'account_id' => Account::where('code', $interestIncomeCode)->firstOrFail()->id,
                'type' => 'credit',
                'amount' => $interestAmount,
                'description' => 'Interest income',
            ];
        }

        return $this->createJournalEntry([
            'description' => $options['description'] ?? 'Loan repayment',
            'date' => $options['date'] ?? now()->toDateString(),
            'source_type' => $options['source_type'] ?? null,
            'source_id' => $options['source_id'] ?? null,
            'notes' => $options['notes'] ?? null,
            'lines' => $lines,
        ]);
    }

    /**
     * Record expense
     */
    public function recordExpense(
        string $expenseAccountCode,
        float $amount,
        string $description,
        string $paymentAccountCode = '1010',
        array $options = []
    ): JournalEntry {
        return $this->createSimpleEntry(
            $expenseAccountCode,
            $paymentAccountCode,
            $amount,
            $description,
            $options
        );
    }

    /**
     * Record income
     */
    public function recordIncome(
        string $incomeAccountCode,
        float $amount,
        string $description,
        string $receivingAccountCode = '1010',
        array $options = []
    ): JournalEntry {
        return $this->createSimpleEntry(
            $receivingAccountCode,
            $incomeAccountCode,
            $amount,
            $description,
            $options
        );
    }

    /**
     * Post a journal entry
     */
    public function postJournalEntry(JournalEntry $journalEntry, User $user = null): bool
    {
        return $journalEntry->post($user);
    }

    /**
     * Create a journal entry line
     */
    private function createJournalEntryLine(JournalEntry $journalEntry, array $lineData): JournalEntryLine
    {
        $this->validateJournalEntryLineData($lineData);

        return $journalEntry->lines()->create([
            'account_id' => $lineData['account_id'],
            'type' => $lineData['type'],
            'amount' => $lineData['amount'],
            'description' => $lineData['description'] ?? null,
        ]);
    }

    /**
     * Validate journal entry data
     */
    private function validateJournalEntryData(array $data): void
    {
        if (empty($data['description'])) {
            throw new \InvalidArgumentException('Description is required');
        }

        if (empty($data['lines']) || !is_array($data['lines'])) {
            throw new \InvalidArgumentException('Lines are required and must be an array');
        }

        if (count($data['lines']) < 2) {
            throw new \InvalidArgumentException('At least two lines are required for double entry');
        }
    }

    /**
     * Validate journal entry line data
     */
    private function validateJournalEntryLineData(array $lineData): void
    {
        if (empty($lineData['account_id'])) {
            throw new \InvalidArgumentException('Account ID is required for journal entry line');
        }

        if (!in_array($lineData['type'], ['debit', 'credit'])) {
            throw new \InvalidArgumentException('Line type must be either debit or credit');
        }

        if (!isset($lineData['amount']) || $lineData['amount'] <= 0) {
            throw new \InvalidArgumentException('Amount must be greater than zero');
        }

        // Verify account exists
        if (!Account::find($lineData['account_id'])) {
            throw new \InvalidArgumentException('Account not found');
        }
    }

    /**
     * Get trial balance
     */
    public function getTrialBalance(Carbon $asOfDate = null): array
    {
        $asOfDate = $asOfDate ?? now();

        $accounts = Account::with(['journalEntryLines' => function ($query) use ($asOfDate) {
            $query->whereHas('journalEntry', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                  ->where('date', '<=', $asOfDate);
            });
        }])->get();

        $trialBalance = [];
        $totalDebits = 0;
        $totalCredits = 0;

        foreach ($accounts as $account) {
            $balance = $account->calculateBalance();

            if (abs($balance) > 0.01) { // Only include accounts with non-zero balances
                // For trial balance, we show positive balances in their normal balance column
                if ($account->normal_balance === 'debit') {
                    $debitBalance = $balance > 0 ? $balance : 0;
                    $creditBalance = $balance < 0 ? abs($balance) : 0;
                } else {
                    $debitBalance = $balance < 0 ? abs($balance) : 0;
                    $creditBalance = $balance > 0 ? $balance : 0;
                }

                $trialBalance[] = [
                    'account' => $account,
                    'debit_balance' => $debitBalance,
                    'credit_balance' => $creditBalance,
                ];

                $totalDebits += $debitBalance;
                $totalCredits += $creditBalance;
            }
        }

        return [
            'accounts' => $trialBalance,
            'total_debits' => $totalDebits,
            'total_credits' => $totalCredits,
            'is_balanced' => abs($totalDebits - $totalCredits) < 0.01,
            'as_of_date' => $asOfDate,
        ];
    }

    /**
     * Get balance sheet
     */
    public function getBalanceSheet(Carbon $asOfDate = null): array
    {
        $asOfDate = $asOfDate ?? now();

        $assets = Account::assets()->with(['journalEntryLines' => function ($query) use ($asOfDate) {
            $query->whereHas('journalEntry', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                  ->where('date', '<=', $asOfDate);
            });
        }])->get();

        $liabilities = Account::liabilities()->with(['journalEntryLines' => function ($query) use ($asOfDate) {
            $query->whereHas('journalEntry', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                  ->where('date', '<=', $asOfDate);
            });
        }])->get();

        $equity = Account::equity()->with(['journalEntryLines' => function ($query) use ($asOfDate) {
            $query->whereHas('journalEntry', function ($q) use ($asOfDate) {
                $q->where('is_posted', true)
                  ->where('date', '<=', $asOfDate);
            });
        }])->get();

        $assetBalances = [];
        $totalAssets = 0;
        foreach ($assets as $asset) {
            $balance = $asset->calculateBalance();
            if (abs($balance) > 0.01) {
                $assetBalances[] = ['account' => $asset, 'balance' => $balance];
                $totalAssets += $balance;
            }
        }

        $liabilityBalances = [];
        $totalLiabilities = 0;
        foreach ($liabilities as $liability) {
            $balance = $liability->calculateBalance();
            if (abs($balance) > 0.01) {
                $liabilityBalances[] = ['account' => $liability, 'balance' => $balance];
                $totalLiabilities += $balance;
            }
        }

        $equityBalances = [];
        $totalEquity = 0;
        foreach ($equity as $equityAccount) {
            $balance = $equityAccount->calculateBalance();
            if (abs($balance) > 0.01) {
                $equityBalances[] = ['account' => $equityAccount, 'balance' => $balance];
                $totalEquity += $balance;
            }
        }

        return [
            'assets' => $assetBalances,
            'liabilities' => $liabilityBalances,
            'equity' => $equityBalances,
            'total_assets' => $totalAssets,
            'total_liabilities' => $totalLiabilities,
            'total_equity' => $totalEquity,
            'total_liabilities_equity' => $totalLiabilities + $totalEquity,
            'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01,
            'as_of_date' => $asOfDate,
        ];
    }

    /**
     * Get income statement
     */
    public function getIncomeStatement(Carbon $startDate = null, Carbon $endDate = null): array
    {
        $startDate = $startDate ?? now()->startOfMonth();
        $endDate = $endDate ?? now()->endOfMonth();

        $income = Account::income()->with(['journalEntryLines' => function ($query) use ($startDate, $endDate) {
            $query->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                  ->whereBetween('date', [$startDate, $endDate]);
            });
        }])->get();

        $expenses = Account::expenses()->with(['journalEntryLines' => function ($query) use ($startDate, $endDate) {
            $query->whereHas('journalEntry', function ($q) use ($startDate, $endDate) {
                $q->where('is_posted', true)
                  ->whereBetween('date', [$startDate, $endDate]);
            });
        }])->get();

        $incomeBalances = [];
        $totalIncome = 0;
        foreach ($income as $incomeAccount) {
            $balance = $incomeAccount->calculateBalance();
            if (abs($balance) > 0.01) {
                $incomeBalances[] = ['account' => $incomeAccount, 'balance' => $balance];
                $totalIncome += $balance;
            }
        }

        $expenseBalances = [];
        $totalExpenses = 0;
        foreach ($expenses as $expenseAccount) {
            $balance = $expenseAccount->calculateBalance();
            if (abs($balance) > 0.01) {
                $expenseBalances[] = ['account' => $expenseAccount, 'balance' => $balance];
                $totalExpenses += $balance;
            }
        }

        $netIncome = $totalIncome - $totalExpenses;

        return [
            'income' => $incomeBalances,
            'expenses' => $expenseBalances,
            'total_income' => $totalIncome,
            'total_expenses' => $totalExpenses,
            'net_income' => $netIncome,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
    }
}
