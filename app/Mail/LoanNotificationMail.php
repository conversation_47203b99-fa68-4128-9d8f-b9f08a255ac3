<?php

namespace App\Mail;

use App\Models\LoanNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class LoanNotificationMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public LoanNotification $notification;

    /**
     * Create a new message instance.
     */
    public function __construct(LoanNotification $notification)
    {
        $this->notification = $notification;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = match($this->notification->type) {
            'payment_reminder' => "Payment Reminder - Loan {$this->notification->loan->loan_number}",
            'overdue' => "Overdue Payment Notice - Loan {$this->notification->loan->loan_number}",
            'bad_debt' => "Important: Account Review Required - Loan {$this->notification->loan->loan_number}",
            default => "Loan Notification - {$this->notification->title}",
        };

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.loan-notification',
            with: [
                'notification' => $this->notification,
                'loan' => $this->notification->loan,
                'borrower' => $this->notification->borrower,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
