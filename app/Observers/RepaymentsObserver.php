<?php

namespace App\Observers;

use App\Models\Repayments;
use App\Services\LoanAccountingService;

class RepaymentsObserver
{
    protected LoanAccountingService $loanAccountingService;

    public function __construct(LoanAccountingService $loanAccountingService)
    {
        $this->loanAccountingService = $loanAccountingService;
    }

    /**
     * Handle the Repayments "created" event.
     */
    public function created(Repayments $repayments): void
    {
        // Record double entry for loan repayment
        $this->loanAccountingService->recordLoanRepayment($repayments);
    }

    /**
     * Handle the Repayments "updated" event.
     */
    public function updated(Repayments $repayments): void
    {
        //
    }

    /**
     * Handle the Repayments "deleted" event.
     */
    public function deleted(Repayments $repayments): void
    {
        //
    }

    /**
     * Handle the Repayments "restored" event.
     */
    public function restored(Repayments $repayments): void
    {
        //
    }

    /**
     * Handle the Repayments "force deleted" event.
     */
    public function forceDeleted(Repayments $repayments): void
    {
        //
    }
}
