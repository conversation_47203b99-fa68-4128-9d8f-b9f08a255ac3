<?php

namespace App\Observers;

use App\Models\Loan;
use App\Services\LoanAccountingService;

class LoanObserver
{
    protected LoanAccountingService $loanAccountingService;

    public function __construct(LoanAccountingService $loanAccountingService)
    {
        $this->loanAccountingService = $loanAccountingService;
    }

    /**
     * Handle the Loan "created" event.
     */
    public function created(Loan $loan): void
    {
        // Record double entry if loan is approved upon creation
        if ($loan->loan_status === 'approved') {
            $this->loanAccountingService->recordLoanDisbursement($loan);
        }
    }

    /**
     * Handle the Loan "updated" event.
     */
    public function updated(Loan $loan): void
    {
        // Check if loan status changed to approved
        if ($loan->wasChanged('loan_status') && $loan->loan_status === 'approved') {
            $this->loanAccountingService->recordLoanDisbursement($loan);
        }
    }

    /**
     * Handle the Loan "deleted" event.
     */
    public function deleted(Loan $loan): void
    {
        //
    }

    /**
     * Handle the Loan "restored" event.
     */
    public function restored(Loan $loan): void
    {
        //
    }

    /**
     * Handle the Loan "force deleted" event.
     */
    public function forceDeleted(Loan $loan): void
    {
        //
    }
}
