<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LoanNotificationService;

class GenerateLoanNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loans:generate-notifications {--type=all : Type of notifications to generate (all, reminders, overdue, bad-debt)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate loan notifications for overdue payments, payment reminders, and bad debt alerts';

    protected LoanNotificationService $notificationService;

    public function __construct(LoanNotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        $this->info("🔔 Generating loan notifications...");

        $results = match($type) {
            'reminders' => ['payment_reminders' => $this->notificationService->generatePaymentReminders()],
            'overdue' => ['overdue_notifications' => $this->notificationService->generateOverdueNotifications()],
            'bad-debt' => ['bad_debt_alerts' => $this->notificationService->generateBadDebtAlerts()],
            default => $this->notificationService->generateAllNotifications(),
        };

        // Display results
        $this->info("📊 Notification Generation Results:");
        $this->table(
            ['Type', 'Count'],
            [
                ['Payment Reminders', $results['payment_reminders'] ?? 0],
                ['Overdue Notifications', $results['overdue_notifications'] ?? 0],
                ['Bad Debt Alerts', $results['bad_debt_alerts'] ?? 0],
            ]
        );

        $total = array_sum($results);

        if ($total > 0) {
            $this->info("✅ Generated {$total} notifications successfully!");
        } else {
            $this->info("ℹ️  No new notifications needed at this time.");
        }

        // Show current stats
        $stats = $this->notificationService->getNotificationStats();
        $this->info("\n📈 Current Notification Statistics:");
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Notifications', $stats['total_notifications']],
                ['Pending Notifications', $stats['pending_notifications']],
                ['Overdue Loans', $stats['overdue_loans']],
                ['Bad Debt Alerts', $stats['bad_debt_alerts']],
                ['Critical Notifications', $stats['critical_notifications']],
            ]
        );

        return Command::SUCCESS;
    }
}
