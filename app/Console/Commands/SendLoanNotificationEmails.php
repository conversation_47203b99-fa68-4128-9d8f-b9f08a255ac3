<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LoanNotification;
use App\Mail\LoanNotificationMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendLoanNotificationEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loans:send-notification-emails {--limit=50 : Maximum number of emails to send}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email notifications to borrowers for pending loan notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = (int) $this->option('limit');

        $this->info("📧 Sending loan notification emails...");

        // Get pending notifications that need to be sent
        $notifications = LoanNotification::with(['loan', 'borrower'])
            ->pending()
            ->whereIn('type', [
                LoanNotification::TYPE_PAYMENT_REMINDER,
                LoanNotification::TYPE_OVERDUE,
            ])
            ->limit($limit)
            ->get();

        if ($notifications->isEmpty()) {
            $this->info("ℹ️  No pending notifications to send.");
            return Command::SUCCESS;
        }

        $sent = 0;
        $failed = 0;

        foreach ($notifications as $notification) {
            try {
                // Check if borrower has email
                if (empty($notification->borrower->email)) {
                    $this->warn("⚠️  Skipping notification {$notification->id}: No email for borrower {$notification->borrower->first_name} {$notification->borrower->last_name}");
                    continue;
                }

                // Send email notification
                $this->sendNotificationEmail($notification);

                // Mark as sent
                $notification->markAsSent();

                $sent++;
                $this->info("✅ Sent notification to {$notification->borrower->email}");

            } catch (\Exception $e) {
                $failed++;
                $this->error("❌ Failed to send notification {$notification->id}: " . $e->getMessage());
                Log::error("Failed to send loan notification email", [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->info("\n📊 Email Sending Results:");
        $this->table(
            ['Status', 'Count'],
            [
                ['Successfully Sent', $sent],
                ['Failed', $failed],
                ['Total Processed', $sent + $failed],
            ]
        );

        return Command::SUCCESS;
    }

    /**
     * Send notification email to borrower
     */
    protected function sendNotificationEmail(LoanNotification $notification): void
    {
        Mail::to($notification->borrower->email, $notification->borrower->first_name . ' ' . $notification->borrower->last_name)
            ->send(new LoanNotificationMail($notification));
    }

    /**
     * Get email subject based on notification type
     */
    protected function getEmailSubject(LoanNotification $notification): string
    {
        return match($notification->type) {
            LoanNotification::TYPE_PAYMENT_REMINDER => "Payment Reminder - Loan {$notification->loan->loan_number}",
            LoanNotification::TYPE_OVERDUE => "Overdue Payment Notice - Loan {$notification->loan->loan_number}",
            default => "Loan Notification - {$notification->title}",
        };
    }

    /**
     * Get email message content
     */
    protected function getEmailMessage(LoanNotification $notification): string
    {
        $borrowerName = $notification->borrower->first_name . ' ' . $notification->borrower->last_name;
        $loanNumber = $notification->loan->loan_number;
        $amount = '$' . number_format($notification->amount, 2);

        $message = "Dear {$borrowerName},\n\n";
        $message .= $notification->message . "\n\n";

        if ($notification->type === LoanNotification::TYPE_PAYMENT_REMINDER) {
            $message .= "Please ensure your payment of {$amount} is made by the due date to avoid late fees.\n\n";
        } elseif ($notification->type === LoanNotification::TYPE_OVERDUE) {
            $message .= "Your payment is now overdue. Please contact us immediately to arrange payment and avoid additional penalties.\n\n";
        }

        $message .= "Loan Details:\n";
        $message .= "- Loan Number: {$loanNumber}\n";
        $message .= "- Amount Due: {$amount}\n";

        if ($notification->due_date) {
            $message .= "- Due Date: " . $notification->due_date->format('M j, Y') . "\n";
        }

        $message .= "\nIf you have any questions or need to discuss payment arrangements, please contact us immediately.\n\n";
        $message .= "Thank you,\n";
        $message .= config('app.name') . " Team";

        return $message;
    }
}
