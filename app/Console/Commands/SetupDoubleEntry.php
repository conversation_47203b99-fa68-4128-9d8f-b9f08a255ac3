<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupDoubleEntry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:double-entry {--fresh : Run fresh migration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup double entry bookkeeping system with all permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up Double Entry Bookkeeping System...');

        // Step 1: Run migrations and seeding
        if ($this->option('fresh')) {
            $this->info('📦 Running fresh migrations and seeding...');
            Artisan::call('migrate:fresh --seed');
            $this->info('✅ Database refreshed and seeded');
        } else {
            $this->info('📦 Running migrations...');
            Artisan::call('migrate');
            $this->info('✅ Migrations completed');

            $this->info('🌱 Seeding accounts...');
            Artisan::call('db:seed', ['--class' => 'AccountSeeder']);
            $this->info('✅ Chart of accounts seeded');
        }

        // Step 2: Generate permissions
        $this->info('🔐 Generating Filament Shield permissions...');
        Artisan::call('shield:generate', ['--all' => true, '--panel' => 'admin']);
        $this->info('✅ Permissions generated');

        // Step 3: Assign permissions to roles
        $this->info('👑 Assigning permissions to admin roles...');

        $superAdminRole = \Spatie\Permission\Models\Role::where('name', 'super_admin')->first();
        $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
        $allPermissions = \Spatie\Permission\Models\Permission::all();

        if ($superAdminRole) {
            $superAdminRole->syncPermissions($allPermissions);
            $this->info('✅ All permissions assigned to super_admin role');
        }

        if ($adminRole) {
            $adminRole->syncPermissions($allPermissions);
            $this->info('✅ All permissions assigned to admin role');
        }

        // Step 4: Display login credentials
        $this->info('');
        $this->info('🎉 Double Entry Bookkeeping System Setup Complete!');
        $this->info('');
        $this->info('📋 Login Credentials:');
        $this->info('   Super Admin: <EMAIL> / password');
        $this->info('   Admin: <EMAIL> / password');
        $this->info('');
        $this->info('📊 Available Features:');
        $this->info('   • Chart of Accounts Management');
        $this->info('   • Automatic Journal Entry Recording');
        $this->info('   • Trial Balance Reports');
        $this->info('   • Balance Sheet & Income Statement');
        $this->info('   • Loan Transaction Integration');
        $this->info('');
        $this->info('🔗 Access the Accounting section in your admin panel!');

        return Command::SUCCESS;
    }
}
